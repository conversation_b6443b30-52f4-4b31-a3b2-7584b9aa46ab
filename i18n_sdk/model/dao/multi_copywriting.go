package dao

import (
	"gorm.io/gorm"
)

// StMultiCopyWriting 多语言文案
type StMultiCopyWriting struct {
	ID      int    `json:"id" gorm:"column:id"`
	Key     string `json:"key" gorm:"key"` //
	Status  int    `json:"status" gorm:"column:status"`
	TxtI18n []byte `json:"txt_i18n" gorm:"column:txt_i18n"`
}

// TableName return user_device_token
func (t *StMultiCopyWriting) TableName() string {
	return "trans_keys"
}

// StMultiCopyWritingVersion 多语言文案版本
type StMultiCopyWritingVersion struct {
	gorm.Model
	ID       int    `json:"id" gorm:"column:id"`
	TransKey string `json:"trans_key" gorm:"trans_key"` //
	Status   int    `json:"status" gorm:"column:status"`
	TxtI18n  []byte `json:"txt_i18n" gorm:"column:txt_i18n"`
	Version  int    `json:"version" gorm:"column:version"`
	OnServer int    `json:"on_use" gorm:"column:on_server"` // 后端
}

// TableName return trans_version
func (t *StMultiCopyWritingVersion) TableName() string {
	return "i18n.trans_version"
}

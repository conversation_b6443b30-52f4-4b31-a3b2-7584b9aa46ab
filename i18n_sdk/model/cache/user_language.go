package cache

import (
	"context"
	"fmt"
	"github.com/go-redis/redis/v8"
	"github.com/sirupsen/logrus"
	"strconv"
	"strings"
)

func getUserLanguageKey(mid int64) string {
	return fmt.Sprintf("data_center:i18n_laguage:%v", mid)
}

// GetUserLanguage 获取用户语种
func GetUserLanguage(rc *redis.Client, ctx context.Context, mid int64) (string, error) {
	key := getUserLanguageKey(mid)
	language, err := rc.Get(ctx, key).Result()
	if err != nil {
		logrus.Errorf("SetUserLanguage err: %v|key: %v|language: %v", err, key, language)
		return "", err
	}

	return language, nil
}

// BatchGetUserLanguage 批量获取用户语种
func BatchGetUserLanguage(rc *redis.Client, ctx context.Context, mids []int64) (map[int64]string, error) {
	if len(mids) == 0 {
		return nil, nil
	}
	keys := make([]string, len(mids))
	for i, mid := range mids {
		keys[i] = getUserLanguageKey(mid)
	}
	languages, err := rc.MGet(ctx, keys...).Result()
	if err != nil {
		logrus.Errorf("BatchGetUserLanguage err: %v|keys: %v", err, keys)
		return nil, err
	}
	mid2Language := make(map[int64]string)
	if len(languages) > 0 {
		for i, value := range languages {
			mid := mids[i]
			if value == nil {
				continue
			}
			lang, ok := value.(string)
			if !ok {
				continue
			}
			mid2Language[mid] = lang
		}
	}
	return mid2Language, nil
}

// BatchGetUserLanguageV2 批量获取用户语种
func BatchGetUserLanguageV2(rc *redis.Client, ctx context.Context, mids []int64) (map[int64]string, error) {
	if len(mids) == 0 {
		return nil, nil
	}
	mid2Language := make(map[int64]string)
	keys := make([]string, len(mids))
	for i, mid := range mids {
		keys[i] = getUserLanguageKeyV2(mid)
	}
	languages, err := rc.MGet(ctx, keys...).Result()
	if err != nil {
		logrus.Errorf("BatchGetUserLanguage err: %v|keys: %v", err, keys)
		return nil, err
	}
	if len(languages) > 0 {
		for _, value := range languages {
			if value == nil {
				continue
			}
			lang, ok := value.(string)
			if !ok {
				continue
			}
			midAndLan := strings.Split(lang, "_")
			if len(midAndLan) > 1 {
				mid, err := strconv.ParseUint(midAndLan[0], 10, 64)
				if err != nil {
					continue
				}
				mid2Language[int64(mid)] = midAndLan[1]
			}
		}
	}
	return mid2Language, nil
}

func getUserLanguageKeyV2(mid int64) string {
	return fmt.Sprintf("data_center:i18n_laguage_v2:%v", mid)
}

package logic

import (
	"context"

	"github.com/sirupsen/logrus"
	"new-gitlab.xunlei.cn/funup/i18n_sdk/model/dao"
	"gorm.io/gorm"
)

type MultiCopyWriting struct {
	ctx context.Context
	db  *gorm.DB
}

const (
	hadRelease = 3
	// batchSize 批量拉取一次拉200条
	batchSize = 200
)

// NewMultiCopyWriting
func NewMultiCopyWriting(ctx context.Context) *MultiCopyWriting {
	return &MultiCopyWriting{ctx: ctx, db: DefaultClient}
}

// GetMultiCopyWriting 获取读语言文案
func (c *MultiCopyWriting) GetMultiCopyWriting(ctx context.Context) ([]*dao.StMultiCopyWriting, error) {
	multiCopyWritingTable := c.db.WithContext(c.ctx).Table(`trans_keys`)
	// 获取总量
	var allCopyWritingNum int64
	err := multiCopyWritingTable.Where(`status=?`, hadRelease).Count(&allCopyWritingNum).Error
	if err == gorm.ErrRecordNotFound {
		logrus.Errorf("GetMultiCopyWriting get_multi_txt_count err: %v", err)
		return nil, err
	}
	if allCopyWritingNum == 0 {
		logrus.Infof("GetMultiCopyWriting get_multi_txt_num nil")
		return nil, nil
	}
	// 批量读取
	totalBatch := allCopyWritingNum / batchSize
	if allCopyWritingNum%batchSize > 0 {
		totalBatch++
	}
	var copyWritings []*dao.StMultiCopyWriting
	for i := 0; i < int(totalBatch); i++ {
		var copyWriting []*dao.StMultiCopyWriting
		offset := i * batchSize
		if err = multiCopyWritingTable.Select("id,`key`,status, txt_i18n").Where(`status=? and on_server =1`, hadRelease).Limit(batchSize).Offset(offset).Find(&copyWriting).Error; err != nil {
			logrus.Errorf("GetMultiCopyWriting get_multi_txt_writing err: %v", err)
			return nil, err
		}
		copyWritings = append(copyWritings, copyWriting...)
	}
	logrus.Infof("GetMultiCopyWriting get_multi_txt_result copyWritings: %+v", copyWritings)
	return copyWritings, nil
}

// GetMultiCopyWritingVersion 获取多语言版本
func (c *MultiCopyWriting) GetMultiCopyWritingVersion(ctx context.Context) (int, error) {
	multiCopyWritingTable := c.db.WithContext(c.ctx).Table(`i18n.trans_version`)
	var version int
	if err := multiCopyWritingTable.Select("MAX(version)").Where(`on_server=?`, 1).Find(&version).Error; err != nil {
		logrus.Errorf("GetMultiCopyWritingVersion get update version err: %v", err)
		return 0, err
	}

	return version, nil
}

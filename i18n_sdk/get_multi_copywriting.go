package i18n_sdk

import (
	"bytes"
	"context"
	"new-gitlab.xunlei.cn/funup/i18n_sdk/config"
	"new-gitlab.xunlei.cn/funup/i18n_sdk/model/cache"
	"regexp"
	"text/template"

	"github.com/sirupsen/logrus"
)

// GetMultiCopyWriting 获取多语言文案
func GetMultiCopyWriting(key, language string, replace []interface{}) (string, bool) {
	// 加载key
	txtOfLanguage, ok := CopyWriting.Load(key)
	if !ok { // 未命中key
		return "", false
	}
	txt, ok := txtOfLanguage.(map[string]string)
	if !ok {
		logrus.Infof("GetMultiCopyWriting txt format err")
		return "", false
	}
	language = compatibleOldVersion(language)
	copyWriting, find := txt[language]
	if !find || copyWriting == "" { // 未命中语言,或者文案为空
		logrus.Infof("GetMultiCopyWriting copyWriting not find lange: %v|key %v｜txt： %+v", language, key, txt)
		return "", false
	}
	if replace == nil { // 无替换文案 返回语句
		return copyWriting, true
	}
	replaceAfter, err := textReplacement(key, copyWriting, replace)
	if err != nil {
		logrus.Errorf("GetMultiCopyWriting repalce err: %v|key: %v|copyWriting: %v", err, key, copyWriting)
		return "", false
	}
	return replaceAfter, true
}

func GetMultiCopyWritingV3(key, language, originText string, replace map[string]string) string {
	// 加载key
	txtOfLanguage, ok := CopyWriting.Load(key)
	if !ok { // 未命中key
		logrus.Infof("GetMultiCopyWritingV3 not_find_txt key: %v", key)
		return originText
	}
	txt, ok := txtOfLanguage.(map[string]string)
	if !ok {
		logrus.Infof("GetMultiCopyWritingV3 txt format err")
		return originText
	}
	language = compatibleOldVersion(language)
	copyWriting, find := txt[language]
	if !find || copyWriting == "" { // 未命中语言或则为空
		logrus.Infof("GetMultiCopyWriting copyWriting not find lange: %v|key %v｜txt： %+v", language, key, txt)
		return originText
	}
	if len(replace) == 0 { // 无替换文案 返回语句
		return copyWriting
	}

	// 有替换文案，则替换操作
	replaceAfter := replacePlaceholders(copyWriting, replace)
	return replaceAfter
}

func replacePlaceholders(text string, values map[string]string) string {
	re := regexp.MustCompile(`\{\{([^{}]+)\}\}`)
	matches := re.FindAllStringSubmatch(text, -1)
	for _, match := range matches {
		placeholder := match[0]
		key := match[1]
		value, ok := values[key]
		if ok {
			text = regexp.MustCompile(placeholder).ReplaceAllString(text, value)
		} else {
			// 如果没有对应的键值，可选择不替换或替换为默认值等
			// 这里选择替换为空字符串
			text = regexp.MustCompile(placeholder).ReplaceAllString(text, "")
		}
	}
	return text
}

func textReplacement(key, copyWriting string, replace []interface{}) (string, error) {
	// 正则匹配到要替换的字段名
	pattern := `{{.(\w+)}}`
	re, err := regexp.Compile(pattern)
	if err != nil {
		logrus.Errorf("textReplacement regexp err: %v|key：%v|copyWriting: %v", err, key, copyWriting)
		return "", err
	}

	// 匹配字符串
	match := re.FindAllStringSubmatch(copyWriting, -1)
	if match == nil {
		logrus.Errorf("textReplacement regexp not match: %v|key：%v|copyWriting: %v", err, key, copyWriting)
		return "", nil
	}
	logrus.Infof("textReplacement replace_success match: %+v", match)
	var buf bytes.Buffer
	t, err := template.New(key).Parse(copyWriting)
	if err != nil {
		logrus.Errorf("textReplacement new_template err: %v|copyWriting: %v", err, copyWriting)
		return "", err
	}

	if len(replace) < len(match) { // 用户替换数据少于需要填充数据
		filling := make([]interface{}, len(match)-len(replace)) // 使用空数据进行补充
		replace = append(replace, filling)
	}
	data := make(map[string]interface{}, len(match))
	for k, v := range match {
		data[v[1]] = replace[k]
	}
	logrus.Infof("textReplacement replace_success match: %+v|data: %+v", match, data)
	err = t.Execute(&buf, data)
	if err != nil {
		logrus.Errorf("textReplacement new_template err: %v|copyWriting: %v", err, copyWriting)
		return "", err
	}
	logrus.Infof("textReplacement replace_success txt: %v", buf.String())
	return buf.String(), nil
}

func compatibleOldVersion(lang string) string {
	if lang == "zh" || lang == "cn" || lang == "" {
		lang = "zh-Hant"
	}
	if lang == "en" {
		lang = "en-US"
	}
	langSet := map[string]interface{}{
		"zh-Hant": nil,
		"zh-CN":   nil,
		"en-US":   nil,
		"ar-EG":   nil,
		"ko-KR":   nil,
		"vi-VN":   nil,
		"tr-TR":   nil,
	}
	if _, ok := langSet[lang]; !ok {
		lang = "zh-Hant"
	}
	return lang
}

func GetMultiCopyWritingByMID(key string, mid int64, replace []interface{}) (string, bool) {
	// 加载key
	txtOfLanguage, ok := CopyWriting.Load(key)
	if !ok { // 未命中key
		logrus.Infof("GetMultiCopyWritingByMID not_find_txt key: %v", key)
		return "", false
	}
	txt, ok := txtOfLanguage.(map[string]string)
	if !ok {
		logrus.Infof("GetMultiCopyWritingByMID txt format err")
		return "", false
	}
	language, err := cache.GetUserLanguage(config.GetRedisClient(), context.Background(), mid)
	if err != nil {
		logrus.Errorf("GetMultiCopyWritingByMID get language err: %v|key: %v", err, key)
		language = compatibleOldVersion(language)
	}
	logrus.Errorf("GetMultiCopyWritingByMID get language: %v|key: %v", language, key)
	copyWriting, find := txt[language]
	if !find || copyWriting == "" { // 未命中语言或者文案为空
		logrus.Infof("GetMultiCopyWriting copyWriting not find lange: %v|key %v｜txt： %+v", language, key, txt)
		return "", false
	}
	if replace == nil { // 无替换文案 返回语句
		return copyWriting, true
	}
	replaceAfter, err := textReplacement(key, copyWriting, replace)
	if err != nil {
		logrus.Errorf("GetMultiCopyWritingByMID repalce err: %v|key: %v|copyWriting: %v", err, key, copyWriting)
		return "", false
	}
	return replaceAfter, true
}

/*
BatchGetMultiCopyWriting 批量获取多语言文案
入参 multiCopyWriting key为文案的key，value为默认文案
如果未命中语言或者key，返回带入的默认文案
*/
func BatchGetMultiCopyWriting(keyAndOriginText map[string]string, language string) map[string]string {
	multiCopyWriting := make(map[string]string, 0)
	// 加载key
	for k, v := range keyAndOriginText {
		txtOfLanguage, ok := CopyWriting.Load(k)
		if !ok { // 未命中key
			multiCopyWriting[k] = v
			continue
		}
		txt, ok := txtOfLanguage.(map[string]string)
		if !ok {
			logrus.Infof("GetMultiCopyWriting txt format err")
			multiCopyWriting[k] = v
			continue
		}
		language = compatibleOldVersion(language)
		copyWriting, find := txt[language]
		if !find { // 未命中语言
			logrus.Infof("GetMultiCopyWriting copyWriting not find lange: %v|key %v", language, k)
			multiCopyWriting[k] = v
			continue
		}
		multiCopyWriting[k] = copyWriting
	}

	return multiCopyWriting
}

// GetMultiCopyWritingV2 获取多语言文案
func GetMultiCopyWritingV2(key, language, originText string, replace []interface{}) string {
	// 加载key
	txtOfLanguage, ok := CopyWriting.Load(key)
	if !ok { // 未命中key
		return originText
	}
	txt, ok := txtOfLanguage.(map[string]string)
	if !ok {
		logrus.Infof("GetMultiCopyWriting txt format err")
		return originText
	}
	language = compatibleOldVersion(language)
	copyWriting, find := txt[language]
	if !find || copyWriting == "" { // 未命中语言或者文案为空
		logrus.Infof("GetMultiCopyWriting copyWriting not find lange: %v|key %v｜txt： %+v", language, key, txt)
		return originText
	}
	if replace == nil { // 无替换文案 返回语句
		return copyWriting
	}
	replaceAfter, err := textReplacement(key, copyWriting, replace)
	if err != nil {
		logrus.Errorf("GetMultiCopyWriting repalce err: %v|key: %v|copyWriting: %v", err, key, copyWriting)
		return originText
	}
	return replaceAfter
}

// GetCopyWritingAllByKey 获取key对应的所有文案
func GetCopyWritingAllByKey(key string, replace []interface{}) map[string]string {
	// 加载key
	multiCopyWriting := make(map[string]string, 0)
	txtOfLanguage, ok := CopyWriting.Load(key)
	if !ok { // 未命中key
		return multiCopyWriting
	}
	txt, ok := txtOfLanguage.(map[string]string)
	if !ok {
		logrus.Infof("GetMultiCopyWriting txt format err")
		return multiCopyWriting
	}
	res := CopyMap(txt)
	if replace == nil {
		return res
	}
	for k, v := range res {
		replaceAfter, err := textReplacement(key, v, replace)
		if err != nil {
			logrus.Errorf("GetMultiCopyWriting repalce err: %v|key: %v|copyWriting: %v", err, key, v)
			return multiCopyWriting
		}
		res[k] = replaceAfter
	}
	return res
}

// GetCopyWritingAllByKeyV3 获取key对应的所有文案
func GetCopyWritingAllByKeyV3(key string, replace map[string]string) map[string]string {
	// 加载key
	multiCopyWriting := make(map[string]string, 0)
	txtOfLanguage, ok := CopyWriting.Load(key)
	if !ok { // 未命中key
		return multiCopyWriting
	}
	txt, ok := txtOfLanguage.(map[string]string)
	if !ok {
		logrus.Infof("GetMultiCopyWriting txt format err")
		return multiCopyWriting
	}
	res := CopyMap(txt)
	if replace == nil {
		return res
	}
	for k, v := range res {
		replaceAfter := replacePlaceholders(v, replace)
		res[k] = replaceAfter
	}
	return res
}

func GetLanguageByMid(mid int64) string {
	language, err := cache.GetUserLanguage(config.GetRedisClient(), context.Background(), mid)
	if err != nil {
		logrus.Errorf("GetLanguageByMid get language err: %v|mid: %v", err, mid)
		language = compatibleOldVersion(language)
	}
	return language
}

func BatchGetLanguageByMids(mids []int64) map[int64]string {
	mid2Language, err := cache.BatchGetUserLanguage(config.GetRedisClient(), context.Background(), mids)
	if err != nil {
		logrus.Errorf("BatchGetLanguageByMids get language err: %v|mids: %v", err, mids)
		mid2Language = coverLanguage(mids)
	}
	return mid2Language
}

func coverLanguage(mids []int64) map[int64]string {
	lang := compatibleOldVersion("")
	res := make(map[int64]string)
	for _, mid := range mids {
		res[mid] = lang
	}
	return res
}

// BatchGetLanguageByMidsV2 批量通过mid获取语种
func BatchGetLanguageByMidsV2(mids []int64) map[int64]string {
	mid2Language, err := cache.BatchGetUserLanguageV2(config.GetRedisClient(), context.Background(), mids)
	if err != nil {
		logrus.Errorf("BatchGetLanguageByMids get language err: %v|mids: %v", err, mids)
		return nil
	}
	return mid2Language
}

// CopyMap 创建并返回一个map的副本
func CopyMap(original map[string]string) map[string]string {
	if original == nil {
		return nil
	}
	copied := make(map[string]string, len(original))
	for key, value := range original {
		copied[key] = value
	}
	return copied
}

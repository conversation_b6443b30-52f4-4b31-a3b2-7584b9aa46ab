package i18n_sdk

import (
	"context"
	"encoding/json"
	"sync"

	"github.com/sirupsen/logrus"
	"new-gitlab.xunlei.cn/funup/i18n_sdk/config"
	"new-gitlab.xunlei.cn/funup/i18n_sdk/model/dao"
	"new-gitlab.xunlei.cn/funup/i18n_sdk/model/logic"
)

type MultiLanguage struct {
	CopyWritingOfLanguage []*CopyWritingOfLanguage `json:"copy_writing_of_language"`
}

// CopyWritingOfLanguage 文案对应的语言种类
type CopyWritingOfLanguage struct {
	Language    string `json:"language"` // 语言种类
	CopyWriting string `json:"copy_writing"`
}

var (
	CopyWriting sync.Map
)

// InitCopyWriting 初始化文案
func InitCopyWriting() {
	log := logrus.WithField("func", "InitCopyWriting")
	ctx := context.Background()
	log.Info("InitCopyWriting in")
	config.Init()

	// 获取版本号
	if err := getVersion(ctx); err != nil {
		log.Errorf("getVersion fail,err:%v", err)
		return
	}
	log.Info("getMultiCopyWriting before")
	//获取文案
	copyWriting, err := getMultiCopyWriting(ctx)
	if err != nil {
		log.Errorf("getMultiCopyWriting fail,err:%v", err)
		return
	}
	log.Info("storyCopyWriting before")
	// 开始整理文案
	storyCopyWriting(copyWriting)
	// 开始心跳
	hearBeat()
	log.Info("InitCopyWriting succ")
}

// getVersion 获取版本id
func getVersion(ctx context.Context) error {
	db := logic.NewMultiCopyWriting(ctx)
	version, err := db.GetMultiCopyWritingVersion(ctx)
	if err != nil {
		logrus.Errorf("InitCopyWriting get_version_err: %v", err)
		return err
	}
	Version = version
	logrus.Infof("getVersion version: %v", Version)
	return nil
}

func getMultiCopyWriting(ctx context.Context) ([]*dao.StMultiCopyWriting, error) {
	db := logic.NewMultiCopyWriting(ctx)
	copyWriting, err := db.GetMultiCopyWriting(ctx)
	if err != nil {
		logrus.Errorf("InitCopyWriting MultiCopyWriting get err: %v", err)
		return nil, err
	}
	return copyWriting, nil
}

// storyCopyWriting 存储文案
func storyCopyWriting(multiCopyWritings []*dao.StMultiCopyWriting) {
	for _, v := range multiCopyWritings {
		if v.TxtI18n == nil {
			logrus.Infof("txt is nil v: %+v", v)
			continue
		}
		var result map[string]string
		if err := json.Unmarshal(v.TxtI18n, &result); err != nil {
			logrus.Errorf("storyCopyWriting err:%v|txt: %v", err, v.TxtI18n)
			continue
		}
		logrus.Infof("storyCopyWriting result: %+v|key:%v", result, v.Key)
		CopyWriting.Store(v.Key, result)
	}
}

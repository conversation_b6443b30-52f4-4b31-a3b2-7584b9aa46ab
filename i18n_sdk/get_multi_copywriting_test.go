package i18n_sdk

import (
	"fmt"
	"testing"
)

func TestGetMultiCopyWriting(t *testing.T) {
	key := "test_hello"
	txt := make(map[string]string)
	txt["en-US"] = "Tengo {{.Bob}} correos {{.Jack}} electrónicos no leídos"
	CopyWriting.Store(key, txt)
	test1 := make(map[string]string)
	test1["test_hello"] = "123"
	test2 := BatchGetLanguageByMidsV2([]int64{300012270, 300012049, 300012050})
	fmt.Printf("BatchGetMultiCopyWriting test2%+v", test2)
}

func TestGetMultiCopyWritingV3(t *testing.T) {
	key := "test_hello"
	txt := make(map[string]string)
	txt["en-US"] = "Tengo %s{{Bob}} correos 00000 {{Jack}} electrónicos no leídos {{Bob}} {{哈哈哈}}%d"
	//txt["en-US"] = "Tengo 你好啊 1111111"
	CopyWriting.Store(key, txt)
	replace := make(map[string]string)
	replace["Bob"] = "123"
	replace["Jack"] = "456"
	text := GetMultiCopyWritingV3(key, "en-US", "呵呵呵", replace)
	fmt.Printf("TestGetMultiCopyWritingV3 text:%+v", text)
}

func TestGetCopyWritingAllByKeyV3(t *testing.T) {
	key := "test_hello_2"
	txt := make(map[string]string)
	txt["en-US"] = "Successfully send gifts to family members, ${+{{number}}combat}"
	txt["zh-CN"] = "给家族成员送礼成功，${+{{number}}战力}"
	CopyWriting.Store(key, txt)
	replace := make(map[string]string)
	replace["number"] = "40"
	m := GetCopyWritingAllByKeyV3(key, replace)
	fmt.Printf("TestGetCopyWritingAllByKeyV3 m:%+v", m)

	replace2 := make(map[string]string)
	replace2["number"] = "10"
	m2 := GetCopyWritingAllByKeyV3(key, replace2)
	fmt.Printf("TestGetCopyWritingAllByKeyV3 m2:%+v", m2)
}

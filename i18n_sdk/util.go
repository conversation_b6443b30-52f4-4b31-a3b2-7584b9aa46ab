package i18n_sdk

import (
	"encoding/json"
	"errors"
	"fmt"
	"github.com/sirupsen/logrus"
)

// TransAreaRegionToLanguage 分区转语种 CS华语区转zh-Hant ES英语区转en-US
func TransAreaRegionToLanguage(areaRegion string) string {
	if areaRegion == "cn" || areaRegion == "" {
		areaRegion = "CS"
	}
	if areaRegion == "en" {
		areaRegion = "ES"
	}
	areaSet := map[string]interface{}{
		"CS": nil,
		"ES": nil,
		"AR": nil,
		"KR": nil,
		"VN": nil,
	}
	if _, ok := areaSet[areaRegion]; !ok {
		logrus.Warnf("areaRegion not match :%s", areaRegion)
		areaRegion = "CS"
	}
	area2Language := map[string]string{
		"CS": "zh-Hant",
		"ES": "en-US",
		"AR": "ar-EG",
		"KR": "ko-KR",
		"VN": "vi-VN",
		"TR": "tr-TR",
	}
	language, ok := area2Language[areaRegion]
	if !ok {
		language = "en-US"
	}
	return language
}

// GetI18nValue 拿多语言对应key的值
func GetI18nValue(i18nJson, lang, def string) string {
	if lang == "zh" || lang == "cn" || lang == "" { //没传，默认用zh-Hant
		lang = "zh-Hant"
	}
	if lang == "en" || lang == "" {
		lang = "en-US"
	}
	langSet := map[string]interface{}{
		"zh-Hant": nil,
		"zh-CN":   nil,
		"en-US":   nil,
		"ar-EG":   nil,
		"ko-KR":   nil,
		"vi-VN":   nil,
		"tr-TR":   nil,
	}
	if _, ok := langSet[lang]; !ok {
		lang = "zh-Hant"
	}
	j := []byte(i18nJson)
	if isJson := json.Valid(j); !isJson {
		logrus.Warnf("i18njson is not json format:%s", i18nJson)
		return def
	}
	m := make(map[string]string)
	json.Unmarshal(j, &m)
	res := m[lang]
	if res == "" {
		res = def
		logrus.Warnf("i18n res is empty,i18nJson:%s,language:%s", i18nJson, lang)
	}
	return res
}

// MsgErr 根据msg进行多语言包装
func MsgErr(msg, language string) error {
	errMsg := GetMultiCopyWritingV2(msg, language, msg, nil)
	return errors.New(errMsg)
}

// MsgVErr 根据msg和参数进行多语言包装
func MsgVErr(msg, language string, params ...interface{}) error {
	errMsg := GetMultiCopyWritingV2(msg, language, msg, nil)
	return errors.New(fmt.Sprintf(errMsg, params...))
}

// MsgErrV3 根据msg进行多语言包装
func MsgErrV3(key, language, originText string, replace map[string]string) error {
	errMsg := GetMultiCopyWritingV3(key, language, originText, replace)
	return errors.New(errMsg)
}

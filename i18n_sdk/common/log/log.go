package log

import (
	"context"
	"strings"

	uuid "github.com/satori/go.uuid"
	"github.com/sirupsen/logrus"
)

func InitLog() {
	logrus.AddHook(&TraceIdHook{})
}

type TraceIdHook struct {
}

func (m *TraceIdHook) Levels() []logrus.Level {
	return logrus.AllLevels
}

const TraceIdKey = "__trace_id"

func (m *TraceIdHook) Fire(entry *logrus.Entry) (err error) {
	var tradeId string
	if entry.Context != nil {
		var ok bool
		tradeId, ok = entry.Context.Value(TraceIdKey).(string)
		if !ok {
			return
		}
	}

	if tradeId == "" {
		return
	}

	entry.Data["trace_id"] = tradeId

	return
}

func ContextWithTraceId(ctx context.Context) (newCtx context.Context) {
	traceId, ok := ctx.Value(TraceIdKey).(string)
	if ok && traceId != "" {
		return
	}

	traceId = UUID()
	newCtx = context.WithValue(ctx, TraceIdKey, traceId)
	return
}

func BgContextWithCancel() (ctx context.Context, cancel context.CancelFunc) {
	ctx, cancel = context.WithCancel(context.Background())
	ctx = ContextWithTraceId(ctx)
	return
}

func GetTraceId(ctx context.Context) (traceId string) {
	traceId, _ = ctx.Value(TraceIdKey).(string)
	return
}

func UUID() string {
	value := uuid.NewV4()

	return strings.ToLower(
		strings.Replace(value.String(), "-", "", -1),
	)
}

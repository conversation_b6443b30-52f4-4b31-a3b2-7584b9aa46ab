package log

import (
	"fmt"

	"github.com/sirupsen/logrus"
)

type GormLogger struct {
	logger *logrus.Logger
}

func (m *GormLogger) Printf(format string, v ...interface{}) {
	logStr := fmt.Sprintf(format, v...)
	m.logger.Info(logStr)
}

func NewGormLogger() *GormLogger {
	log := logrus.New()

	log.SetFormatter(&logrus.JSONFormatter{
		TimestampFormat: "2006-01-02 15:04:05",
	})

	return &GormLogger{logger: log}
}

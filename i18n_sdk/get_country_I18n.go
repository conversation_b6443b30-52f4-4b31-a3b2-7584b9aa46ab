package i18n_sdk

import (
	"encoding/json"
	"github.com/sirupsen/logrus"
	"new-gitlab.xunlei.cn/funup/i18n_sdk/common/country"
	"sync"
)

var once sync.Once
var CountryMap sync.Map

func GetCountryI18n(origin, language string) string {
	once.Do(LoadCountryJson)
	language = compatibleOldVersion(language)
	txtOfLanguage, ok := CountryMap.Load(origin)
	if !ok { // 未命中key
		logrus.Infof("GetCountryI18n not_find_txt key: %v", origin)
		return origin
	}
	txt, ok := txtOfLanguage.(map[string]string)
	if !ok {
		logrus.Infof("GetCountryI18n txt format err")
		return origin
	}
	if txt != nil && txt[language] != "" {
		return txt[language]
	}
	return origin
}

// 不会变的国家列表，写死即可，性能更佳
func LoadCountryJson() {
	//from  https://consul.hiyaparty.com/ui/dc1/kv/hiya/prod/service/app_config/country.json/edit

	var temp struct {
		VcodeConfig []struct {
			Name string `json:"name"`
		} `json:"vcode_config"`
	}
	json.Unmarshal([]byte(country.CountryJsonStr), &temp)
	i := 0
	for _, v := range temp.VcodeConfig {
		i18nName := GetCopyWritingAllByKey(v.Name, nil) // 所有语种翻译
		if i18nName != nil {
			for _, name := range i18nName {
				CountryMap.Store(name, i18nName) // 存储某一语种对应其他的翻译
			}

		}
		i++
	}
	logrus.Infof("LoadCountryJson end,country count: %d", i)

}

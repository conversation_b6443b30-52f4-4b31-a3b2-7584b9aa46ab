package config

import (
	"github.com/sirupsen/logrus"
	"new-gitlab.xunlei.cn/funup/i18n_sdk/common/log"
	"new-gitlab.xunlei.cn/funup/i18n_sdk/model/logic"
	"gorm.io/driver/mysql"
	"gorm.io/gorm"
	"gorm.io/gorm/logger"
)

var MySQLConf struct {
	Url         string `json:"url"`
	MaxOpenConn int    `json:"max_open_conn"`
	MaxIdleConn int    `json:"max_idle_conn"`
}

func InitMySqlClient() {
	logrus.Infof("InitMySqlClient in, MySQLConf.Url:%s", MySQLConf.Url)
	sqlLogger := logger.New(
		log.NewGormLogger(),
		logger.Config{
			LogLevel: logger.Silent,
		},
	)
	var err error
	logic.DefaultClient, err = gorm.Open(mysql.Open(MySQLConf.Url), &gorm.Config{Logger: sqlLogger})
	if err != nil {
		logrus.Fatalf("open mysql conn failed.err:%v", err)
	}
	db, err := logic.DefaultClient.DB()
	if err != nil {
		logrus.Fatalf("DefaultClient.DB() failed.err:%v", err)
	}
	db.SetMaxOpenConns(MySQLConf.MaxOpenConn)
	db.SetMaxIdleConns(MySQLConf.MaxIdleConn)
	logrus.Infof("InitMySqlClient succ, MySQLConf.Url:%s", MySQLConf.Url)
}

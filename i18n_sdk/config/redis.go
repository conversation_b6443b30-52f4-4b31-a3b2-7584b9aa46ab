package config

import (
	"context"
	"fmt"
	"time"

	"github.com/go-redis/redis/v8"
	"github.com/sirupsen/logrus"
)

type RedisConfigFormat struct {
	Addr     string `yaml:"addr" validate:"required"`
	Password string `yaml:"password"`
	Db       int    `yaml:"db"`
	PoolSize int    `yaml:"pool_size"`
}

var redisConfig RedisConfigFormat
var rdb *redis.Client

func GetRedisClient() *redis.Client {
	return rdb
}

func InitRedisClient() {
	rdb = redis.NewClient(&redis.Options{
		Network:     "tcp",
		Addr:        redisConfig.Addr,
		Password:    redisConfig.Password,
		DB:          redisConfig.Db,
		PoolSize:    redisConfig.PoolSize,
		IdleTimeout: time.Minute * 30,
	})
	fmt.Println(redisConfig.Addr)
	if err := rdb.Ping(context.Background()).Err(); err != nil {
		logrus.Errorf("withRedis ping redis failed. %s", err)
	}
}

package config

import (
	"github.com/sirupsen/logrus"
	"new-gitlab.xunlei.cn/funup/i18n_sdk/common/log"
	"os"
)

var (
	Env           string
	Platform      string
	ConsulAddress string
	ConsulPrefix  string
)

const (
	// EnvProd 正式环境
	EnvProd = "prod"
	// EnvDev 开发环境
	EnvDev = "dev"
)

// IsProd 判断是否是正式环境
func IsProd() bool {
	return Env == EnvProd
}

// IsDev 判断是否是开发环境
func IsDev() bool {
	return Env == EnvDev
}

func Init() {
	env := os.Getenv("stage")
	Platform = os.Getenv("platform")
	Env = env
	logrus.Infof("init config, env:%s", env)
	log.InitLog()
	if Env == "" {
		Env = "dev"
	}
	logrus.Infof("env: %v", Env)
	// 替换consul配置的前缀
	switch Env {
	case "test":
		MySQLConf.Url = "root1:Ffratj0aUxi3@tcp(rm-t4n4ow646890p3dyc.mysql.singapore.rds.aliyuncs.com:3306)/i18n?parseTime=true&loc=Local&charset=utf8mb4,utf8"
		//MySQLConf.Url = "root1:VUQrN5LHbuoM@tcp(rm-t4n3fpzy637we5n8j.mysql.singapore.rds.aliyuncs.com:3306)/i18n?parseTime=true&loc=Local&charset=utf8mb4,utf8"
		// ConsulAddress, ConsulPrefix = "consul:8500", "hiya/test/service/i18n_sdk/"
		redisConfig.Addr = "r-t4nmx1numr9lz5ce5x.redis.singapore.rds.aliyuncs.com:6379"
		redisConfig.Password = "P1AB9k4v4Rcx"
		redisConfig.PoolSize = 20
	case "prod":
		redisConfig.Addr = "r-t4ncdnpby2vcqmd177.redis.singapore.rds.aliyuncs.com:6379"
		redisConfig.Password = "jitCb48v6TSj"
		redisConfig.PoolSize = 20
		MySQLConf.Url = "root1:d3ImAy4Oux7I@tcp(rm-t4nr82a4it85fzrc8.mysql.singapore.rds.aliyuncs.com)/i18n?parseTime=true&loc=Local&charset=utf8mb4,utf8"
	case "dev":
		MySQLConf.Url = "root1:Ffratj0aUxi3@tcp(rm-t4n4ow646890p3dyc.mysql.singapore.rds.aliyuncs.com:3306)/i18n?parseTime=true&loc=Local&charset=utf8mb4,utf8"
		redisConfig.Addr = "r-t4nmx1numr9lz5ce5x.redis.singapore.rds.aliyuncs.com:6379"
		redisConfig.Password = "P1AB9k4v4Rcx"
		redisConfig.PoolSize = 20
	default:
		logrus.Panicf("unsupported env: %s", env)
		return
	}
	MySQLConf.MaxOpenConn = 2
	MySQLConf.MaxIdleConn = 1
	// consul.Init(ConsulAddress, ConsulPrefix)
	InitMySqlClient()
	InitRedisClient()
}

package i18n_sdk

import (
	"bytes"
	"context"
	"encoding/json"
	"io/ioutil"
	"net/http"
	"time"

	"github.com/sirupsen/logrus"
)

// Version 版本id全局使用
var Version int

// MultiCopyWritingWithVersion 文案与版本
type MultiCopyWritingWithVersion struct {
	Data *Data `json:"data"`
	Ret  int   `json:"ret"`
}

type Data struct {
	Version             int                `json:"version"` // 版本
	CopywritingInfoList []*CopyWritingInfo `json:"copywriting_info_List"`
}
type CopyWritingInfo struct {
	Key     string `json:"key"`
	TxtI18N string `json:"txt_i18n"`
}

type UpdateVersion struct {
	Version int `json:"version"`
}

// HearBeat 心跳监听文案
func hearBeat() {
	go func() {
		defer func() {
			if iRec := recover(); iRec != nil {
				logrus.Errorf("heart beat panic. error: %s", iRec)
			}
		}()
		for {
			time.Sleep(60 * time.Second) // sdk加载后启动心跳
			// 发送http请求,获取已更新key 及最新版本id
			multiCopyWritingWithVersion, err := getUpdateCopyWriting(context.Background())
			if err != nil {
				logrus.Errorf("hearBeat getUpdateCopyWriting err: %v", err)
				return
			}
			logrus.Infof("hearBeat success_multiCopyWritingWithVersion: %+v", multiCopyWritingWithVersion)
			updateStory(multiCopyWritingWithVersion)
		}
	}()
	return
}

func getUpdateCopyWriting(ctx context.Context) (*MultiCopyWritingWithVersion, error) {
	url := "http://api-in.imfunup.com/wefun_i18n/cms/key/v1/heart_beat_and_update"
	//if !config.IsProd() { // 测试环境直接读现网配置
	//	url = "http://test-api-in.imfunup.com/wefun_i18n/cms/key/v1/heart_beat_and_update"
	//}

	data := UpdateVersion{
		Version: Version,
	}
	payload, _ := json.Marshal(data)
	request, err := http.NewRequestWithContext(ctx, http.MethodPost, url, bytes.NewReader(payload))
	request.Header.Set("Content-Type", "application/json")
	client := &http.Client{}
	resp, err := client.Do(request)
	if err != nil {
		logrus.Errorf("http do err: %v", err)
		return nil, err
	}
	defer resp.Body.Close()
	body, err := ioutil.ReadAll(resp.Body)
	if err != nil {
		logrus.Errorf("getUpdateCopyWriting ReadAll err: %v|body: %v", err, resp.Body)
		return nil, err
	}
	multiCopyWritingWithVersion := &MultiCopyWritingWithVersion{}
	if err = json.Unmarshal(body, multiCopyWritingWithVersion); err != nil {
		logrus.Errorf("getUpdateCopyWriting unmarshal err: %v|body: %v", err, string(body))
		return nil, err
	}

	logrus.Infof("multiCopyWritingWithVersion: %+v", multiCopyWritingWithVersion)
	return multiCopyWritingWithVersion, nil
}

// updateStory 更新存储
func updateStory(multiCopyWritingWithVersion *MultiCopyWritingWithVersion) {
	if multiCopyWritingWithVersion == nil || multiCopyWritingWithVersion.Data == nil {
		return
	}
	for _, v := range multiCopyWritingWithVersion.Data.CopywritingInfoList {
		var result map[string]string
		if err := json.Unmarshal([]byte(v.TxtI18N), &result); err != nil {
			logrus.Errorf("storyCopyWriting err:%v|txt: %v", err, v.TxtI18N)
			continue
		}
		CopyWriting.Store(v.Key, result)
		logrus.Infof("update storyCopyWriting key: %v|result: %+v", v.Key, result)
	}
	if Version < multiCopyWritingWithVersion.Data.Version {
		Version = multiCopyWritingWithVersion.Data.Version
	}
}

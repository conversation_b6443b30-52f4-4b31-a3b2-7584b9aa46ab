<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="VcsDirectoryMappings">
    <mapping directory="$PROJECT_DIR$/account_system" vcs="Git" />
    <mapping directory="$PROJECT_DIR$/data_center" vcs="Git" />
    <mapping directory="$PROJECT_DIR$/guild" vcs="Git" />
    <mapping directory="$PROJECT_DIR$/hiya_audit" vcs="Git" />
    <mapping directory="$PROJECT_DIR$/hiya_push_strategy" vcs="Git" />
    <mapping directory="$PROJECT_DIR$/hiya_statistics" vcs="Git" />
    <mapping directory="$PROJECT_DIR$/i18n_sdk" vcs="Git" />
    <mapping directory="$PROJECT_DIR$/live_opserver" vcs="Git" />
    <mapping directory="$PROJECT_DIR$/live_server" vcs="Git" />
    <mapping directory="$PROJECT_DIR$/m_activity_billboard" vcs="Git" />
    <mapping directory="$PROJECT_DIR$/manage_guild" vcs="Git" />
    <mapping directory="$PROJECT_DIR$/me-live-trade" vcs="Git" />
    <mapping directory="$PROJECT_DIR$/rc_collector" vcs="Git" />
    <mapping directory="$PROJECT_DIR$/rc_reconciler" vcs="Git" />
    <mapping directory="$PROJECT_DIR$/rc_snapshot" vcs="Git" />
    <mapping directory="$PROJECT_DIR$/recommend_tagging" vcs="Git" />
    <mapping directory="$PROJECT_DIR$/room_system" vcs="Git" />
    <mapping directory="$PROJECT_DIR$/room_treasure_chest" vcs="Git" />
    <mapping directory="$PROJECT_DIR$/shopping_mall" vcs="Git" />
    <mapping directory="$PROJECT_DIR$/user_traffic_statistic" vcs="Git" />
    <mapping directory="$PROJECT_DIR$/welfare_center" vcs="Git" />
    <mapping directory="$PROJECT_DIR$/yaplay_activity_proxy" vcs="Git" />
    <mapping directory="$PROJECT_DIR$/yaplay_proto" vcs="Git" />
    <mapping directory="$PROJECT_DIR$/yaplaygateway" vcs="Git" />
  </component>
</project>
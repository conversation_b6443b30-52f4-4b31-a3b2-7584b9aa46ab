package vip_center_grpc

import (
	"context"
	"fmt"
	middleware "github.com/grpc-ecosystem/go-grpc-middleware"
	retry "github.com/grpc-ecosystem/go-grpc-middleware/retry"
	prometheus "github.com/grpc-ecosystem/go-grpc-prometheus"
	"github.com/sirupsen/logrus"
	"google.golang.org/grpc"
	"google.golang.org/grpc/credentials/insecure"
	"new-gitlab.xunlei.cn/funup/yaplay_proto/vip_center"
	"os"
	"time"
)

const (
	// VipCenterSvcPort 是对应服务启动时的端口，保证正确，且不能随意变更！！！
	VipCenterSvcPort = 22124
)

var (
	grpcUrl  string
	grpcConn *grpc.ClientConn
	client   vip_center.AppServiceClient
)

func Init() {
	env := os.Getenv("stage")
	grpcUrl = fmt.Sprintf("vip-center.hiya-%s.svc.cluster.local:%d", env, VipCenterSvcPort)
	initVipCenterGrpcConn(grpcUrl)
	grpcConn = getGrpcConn()
	client = vip_center.NewAppServiceClient(grpcConn)
}

func getGrpcConn() *grpc.ClientConn {
	return grpcConn
}

func GetGrpcClient() vip_center.AppServiceClient {
	return client
}

func initVipCenterGrpcConn(grpcUrl string) {
	var err error
	var ctx, cancelFunc = context.WithTimeout(context.Background(), 5*time.Second)
	defer cancelFunc()

	grpcConn, err = grpc.DialContext(ctx, grpcUrl,
		grpc.WithTransportCredentials(insecure.NewCredentials()),
		grpc.WithBlock(),
		grpc.WithDefaultServiceConfig(`{"loadBalancingPolicy": "round_robin"}`),
		grpc.WithUnaryInterceptor(middleware.ChainUnaryClient(
			prometheus.UnaryClientInterceptor,
			retry.UnaryClientInterceptor(
				retry.WithPerRetryTimeout(60*time.Second),
				retry.WithMax(3),
			),
		)),
	)
	if err != nil {
		logrus.Errorf(`initVipCenterGrpcConn err. urlPrefix:%s`, grpcUrl)
		//logrus.WithError(err).WithField(`urlPrefix`, grpcUrl).Panic("did not connect.")
	} else {
		logrus.Warnf(`initVipCenterGrpcConn success. urlPrefix:%s`, grpcUrl)
	}
}

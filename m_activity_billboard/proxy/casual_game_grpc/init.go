package casual_game_grpc

import (
	"context"
	"fmt"
	"os"
	"time"

	middleware "github.com/grpc-ecosystem/go-grpc-middleware"
	retry "github.com/grpc-ecosystem/go-grpc-middleware/retry"
	prometheus "github.com/grpc-ecosystem/go-grpc-prometheus"
	"github.com/sirupsen/logrus"
	"google.golang.org/grpc"
	"google.golang.org/grpc/credentials/insecure"
	"new-gitlab.xunlei.cn/funup/yaplay_proto/casual_game"
)

const (
	// CasualGameSvcPort 是对应服务启动时的端口，保证正确，且不能随意变更！！！
	CasualGameSvcPort = 35568
)

var (
	GrpcUrl string

	grpcConn                  *grpc.ClientConn
	newFairyGameServiceClient casual_game.FairyGameServiceClient
	coreGameServiceClient     casual_game.CoreGameServiceClient
	appServiceClient          casual_game.AppServiceClient
)

func Init() {
	var err error
	var ctx, cancelFunc = context.WithTimeout(context.Background(), 5*time.Second)
	defer cancelFunc()

	env := os.Getenv("stage")
	grpcUrl := fmt.Sprintf("casual-game.hiya-%s.svc.cluster.local:%d", env, CasualGameSvcPort)

	grpcConn, err = grpc.DialContext(ctx, grpcUrl,
		grpc.WithTransportCredentials(insecure.NewCredentials()),
		grpc.WithBlock(),
		grpc.WithDefaultServiceConfig(`{"loadBalancingPolicy": "round_robin"}`),
		grpc.WithUnaryInterceptor(middleware.ChainUnaryClient(
			prometheus.UnaryClientInterceptor,
			retry.UnaryClientInterceptor(
				retry.WithPerRetryTimeout(60*time.Second),
				retry.WithMax(3),
			),
		)),
	)
	if err != nil {
		logrus.Errorf(`initCasualGameGrpcConnK8s err. urlPrefix:%s`, grpcUrl)
		//logrus.WithError(err).WithField(`urlPrefix`, grpcUrl).Panic("did not connect.")
	} else {
		logrus.Infof(`initCasualGameGrpcConnK8s success. urlPrefix:%s`, grpcUrl)
	}
	newFairyGameServiceClient = casual_game.NewFairyGameServiceClient(grpcConn)
	coreGameServiceClient = casual_game.NewCoreGameServiceClient(grpcConn)
	appServiceClient = casual_game.NewAppServiceClient(grpcConn)
}

func GetFairyGameServiceClient() casual_game.FairyGameServiceClient {
	return newFairyGameServiceClient
}

func GetCoreGameServiceClient() casual_game.CoreGameServiceClient {
	return coreGameServiceClient
}

func GetAppServiceClient() casual_game.AppServiceClient {
	return appServiceClient
}

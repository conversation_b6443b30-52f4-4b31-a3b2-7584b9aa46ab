package activity_h5

import (
	"m_activity_billboard/model/activity_tp"
	"m_activity_billboard/proxy/proxy_query"
)

// HandleScore 处理单个榜单数据的积分比率
func HandleScore(conf activity_tp.TpRankConfig, rankData *proxy_query.RankData) {
	rankData.CoinValue = int64(conf.RankRadio * float64(rankData.CoinValue))
	for k := range rankData.TopUser {
		rankData.TopUser[k].CoinValue = int64(float64(rankData.TopUser[k].CoinValue) * conf.RankRadio)
	}
}

// HandleScoreList 处理榜单列表的积分比率
func HandleScoreList(conf activity_tp.TpRankConfig, list []*proxy_query.RankData) {
	for k := range list {
		HandleScore(conf, list[k])
	}
}

// HideTopNData 隐藏前N名的敏感数据
// currentUserMid: 当前请求用户的ID，如果该用户在TopN中，则不隐藏数据
func HideTopNData(conf activity_tp.TpRankConfig, list []*proxy_query.RankData, currentUserMid int64) {
	if conf.HiddenTopN <= 0 || len(list) == 0 {
		return
	}

	// 检查当前用户是否在TopN榜单中
	isCurrentUserInTopN := false
	hideCount := conf.HiddenTopN
	if hideCount > len(list) {
		hideCount = len(list)
	}

	// 如果当前用户已登录，检查是否在TopN中
	if currentUserMid > 0 {
		for i := 0; i < hideCount; i++ {
			if list[i].UserInfo.Mid == currentUserMid {
				isCurrentUserInTopN = true
				break
			}
		}
	}

	// 如果当前用户在TopN中，不隐藏任何数据
	if isCurrentUserInTopN {
		return
	}

	// 隐藏前N名的CoinValue数据, -1表示隐藏
	for i := 0; i < hideCount; i++ {
		list[i].CoinValue = -1

		for j := range list[i].TopUser {
			list[i].TopUser[j].CoinValue = -1
		}
	}
}

// HideTopNRelationData 隐藏前N名的关系榜单敏感数据, -1表示隐藏
func HideTopNRelationData(conf activity_tp.TpRankConfig, list []*proxy_query.RelationRankData) {
	if conf.HiddenTopN <= 0 || len(list) == 0 {
		return
	}

	hideCount := conf.HiddenTopN
	if hideCount > len(list) {
		hideCount = len(list)
	}

	for i := 0; i < hideCount; i++ {
		list[i].AddIntimacyValue = -1
	}
}

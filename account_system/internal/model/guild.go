package model

type GuildMember struct {
	Mid     int64 `gorm:"column:mid"`
	Role    int64 `gorm:"column:role"`
	GuildID int64 `gorm:"column:guild_id"`
}

func (g GuildMember) TableName() string {
	return "hiya_guild.guild_member"
}

type JoinGuildLog struct {
	Mid        int64 `gorm:"column:mid"`
	JoinStatus int64 `gorm:"column:join_status"`
	GuildID    int64 `gorm:"column:guild_id"`
}

func (g JoinGuildLog) TableName() string {
	return "hiya_guild.guild_join_log"
}

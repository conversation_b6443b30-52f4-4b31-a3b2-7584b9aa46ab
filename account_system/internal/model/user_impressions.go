package model

import "fmt"

type UserImpressions struct {
	ID            int64  `gorm:"column:id;primary_key"`
	Mid           int64  `gorm:"column:mid"`
	ImpressionIds string `gorm:"column:impression_ids"`
}

func (d *UserImpressions) TableName() string {
	return fmt.Sprintf("me_user.user_impressions")
}

type Impressions struct {
	ID      int64  `gorm:"column:id;primary_key"`
	WordsZh string `gorm:"column:words_zh"`
	WordsEn string `gorm:"column:words_en"`
	WordsAR string `gorm:"column:words_ar"`
	WordsKR string `gorm:"column:words_kr"`
	WordsVN string `gorm:"column:words_vn"`
	WordsTR string `gorm:"column:words_tr"`
}

func (d *Impressions) TableName() string {
	return fmt.Sprintf("me_user.impressions")
}

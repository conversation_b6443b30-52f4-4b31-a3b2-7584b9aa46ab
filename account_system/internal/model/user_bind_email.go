package model

import (
	"fmt"
	"time"
)

type BindEmail struct {
	ID           int64     `gorm:"column:id;primary_key"`
	Mid          int64     `gorm:"column:mid"`
	Email        string    `gorm:"column:email"`
	Salt         string    `gorm:"column:salt"`
	HashPassword string    `gorm:"column:hash_password"`
	BindType     int32     `gorm:"column:bind_type"`
	BindDesc     string    `gorm:"column:bind_desc"`
	CreatedAt    time.Time `gorm:"column:created_at"`
	UpdatedAt    time.Time `gorm:"column:updated_at"`
}

func (d *BindEmail) TableName() string {
	return fmt.Sprintf("cn_me_account.user_bind_email")
}

type BindEmailRecord struct {
	Id        int64     `gorm:"column:id;primary_key"`
	Mid       int64     `gorm:"column:mid"`
	SrcEmail  string    `gorm:"column:src_email"`
	DstEmail  string    `gorm:"column:dst_email"`
	Operator  string    `gorm:"column:operator"`
	CreatedAt time.Time `gorm:"column:created_at"`
}

func (d *BindEmailRecord) TableName() string {
	return fmt.Sprintf("cn_me_account.bind_email_record")
}

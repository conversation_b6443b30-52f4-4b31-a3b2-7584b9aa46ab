package model

import (
	"time"
)

// DeviceLevelRecord 设备分级记录
type DeviceLevelRecord struct {
	ID                   int64     `json:"id" gorm:"primaryKey;autoIncrement"`
	HDid                 string    `json:"h_did" gorm:"column:h_did;type:varchar(128);not null;comment:设备唯一标识符;index:idx_h_did"`
	HDt                  int32     `json:"h_dt" gorm:"column:h_dt;type:int;not null;comment:操作系统类型（0:Android 1:iOS）"`
	HOs                  string    `json:"h_os" gorm:"column:h_os;type:varchar(32);not null;comment:操作系统版本号"`
	HModel               string    `json:"h_model" gorm:"column:h_model;type:varchar(128);comment:设备型号"`
	HAppid               string    `json:"h_appid" gorm:"column:h_appid;type:varchar(32);comment:应用版本唯一标识"`
	HAv                  string    `json:"h_av" gorm:"column:h_av;type:varchar(32);comment:版本号"`
	HM                   int64     `json:"h_m" gorm:"column:h_m;type:bigint;comment:用户ID"`
	HCh                  string    `json:"h_ch" gorm:"column:h_ch;type:varchar(32);comment:渠道"`
	HCarrier             string    `json:"h_carrier" gorm:"column:h_carrier;type:varchar(32);comment:sim卡"`
	HAreaRegion          string    `json:"h_area_region" gorm:"column:h_area_region;type:varchar(32);comment:大区"`
	CpuBrand             string    `json:"cpu_brand" gorm:"column:cpu_brand;type:varchar(64);comment:CPU品牌"`
	CpuCoreCount         string    `json:"cpu_core_count" gorm:"column:cpu_core_count;type:int;comment:CPU核心数"`
	CpuFrequency         string    `json:"cpu_frequency" gorm:"column:cpu_frequency;type:varchar(32);comment:CPU频率（单位：MHz）"`
	MemoryCapacity       string    `json:"memory_capacity" gorm:"column:memory_capacity;type:decimal(10,2);comment:内存容量（单位：GB）"`
	ScreenWidth          string    `json:"screen_width" gorm:"column:screen_width;type:decimal(10,2);comment:屏幕分辨率-宽度"`
	ScreenHeight         string    `json:"screen_height" gorm:"column:screen_height;type:decimal(10,2);comment:屏幕分辨率-高度"`
	AndroidApiLevel      string    `json:"android_api_level" gorm:"column:android_api_level;type:int;comment:Android API级别"`
	NetType              string    `json:"net_type" gorm:"column:net_type;type:varchar(16);comment:网络类型"`
	MemoryRemainCapacity string    `json:"memory_remain_capacity" gorm:"column:memory_remain_capacity;type:decimal(10,2);comment:内存剩余容量（单位：GB）"`
	DeviceLevel          int32     `json:"device_level" gorm:"column:device_level;type:int;not null;comment:设备等级枚举值（1:低端设备 2:中端设备 3:高端设备）"`
	Score                int32     `json:"score" gorm:"column:score;type:int;comment:综合评分结果"`
	QuotaScores          string    `json:"quota_scores" gorm:"column:quota_scores;type:json;comment:指标分数"`
	UpdateTime           time.Time `json:"update_time" gorm:"column:update_time;type:datetime;not null;comment:记录更新时间"`
	CreateTime           time.Time `json:"create_time" gorm:"column:create_time;type:datetime;not null;default:CURRENT_TIMESTAMP;comment:记录创建时间"`
	ErrMsg               string    `json:"err_msg" gorm:"column:err_msg;type:varchar(1024);comment:错误信息"`
}

// TableName 指定表名
func (DeviceLevelRecord) TableName() string {
	return "cn_me_account.device_level_record"
}

// DeviceLevelEnum 设备等级枚举
type DeviceLevelEnum int32

const (
	DeviceLevelUnknown DeviceLevelEnum = 0 // 未知等级
	DeviceLevelLow     DeviceLevelEnum = 1 // 低端设备
	DeviceLevelMedium  DeviceLevelEnum = 2 // 中端设备
	DeviceLevelHigh    DeviceLevelEnum = 3 // 高端设备
)

// String 返回枚举字符串
func (e DeviceLevelEnum) String() string {
	switch e {
	case DeviceLevelUnknown:
		return "未知等级"
	case DeviceLevelLow:
		return "低端设备"
	case DeviceLevelMedium:
		return "中端设备"
	case DeviceLevelHigh:
		return "高端设备"
	default:
		return "未知等级"
	}
}

// Int32 返回枚举数值
func (e DeviceLevelEnum) Int32() int32 {
	return int32(e)
}

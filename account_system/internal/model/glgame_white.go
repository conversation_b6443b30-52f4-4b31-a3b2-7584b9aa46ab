package model

import "time"

type GlgameWhiteState int64

const (
	GlgameWhiteStateNormal GlgameWhiteState = 1 //正常
	GlgameWhiteStateDelete GlgameWhiteState = 2 //删除
)

type GlgameWhite struct {
	ID        int64            `gorm:"column:id"`
	Mid       int64            `gorm:"column:mid"`
	GlGame    string           `gorm:"column:gl_game"`  // 游戏名称
	Operator  string           `gorm:"column:operator"` // 操作人
	State     GlgameWhiteState `gorm:"column:state"`    // 状态 1-正常 2-删除
	CreatedAt time.Time        `gorm:"column:created_at"`
	UpdatedAt time.Time        `gorm:"column:updated_at"`
}

func (g GlgameWhite) TableName() string {
	return "cn_me_account.glgame_whites"
}

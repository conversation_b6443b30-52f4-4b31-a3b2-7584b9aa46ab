package model

type User struct {
	ID             int64         `bson:"_id"`
	CT             int64         `bson:"ct"`
	Epaulet        interface{}   `bson:"epaulet"`
	EpauletList    []interface{} `bson:"epauletlist"`
	Gender         int64         `bson:"gender"`
	Identify       int64         `bson:"identify"`
	IsBind         int64         `bson:"isbind"`
	IsReg          int64         `bson:"isreg"`
	IsRobotName    int64         `bson:"isrobotname"`
	Medal          interface{}   `bson:"medal"`
	Name           string        `bson:"name"`
	NormalPhone    string        `bson:"normalphone"`
	Phone          string        `bson:"phone"`
	PhoneBindID    int64         `bson:"phonebindid"`
	Profession     interface{}   `bson:"profession"`
	RegionCode     string        `bson:"region_code"`
	RegisterStatus int64         `bson:"register_status"`
	RT             int64         `bson:"rt"`
	UT             int64         `bson:"ut"`
	ZYID           string        `bson:"zyid"`
	LoginPackage   string        `bson:"login_package"`
	LoginSDKApp    string        `bson:"login_sdk_app"`
	LoginTS        int64         `bson:"login_ts"`
	RegPackage     string        `bson:"reg_package"`
	Avatar         int64         `bson:"avatar"`
	Birth          int64         `bson:"birth"`
	Location       struct {
		Lat      int    `bson:"lat"`
		Lon      int    `bson:"lon"`
		Province string `bson:"province"`
		City     string `bson:"city"`
	} `bson:"location"`
	IsDefaultAvatar int64 `bson:"is_default_avatar"`
	BindingSwitch   int64 `bson:"bindingswitch"`
}

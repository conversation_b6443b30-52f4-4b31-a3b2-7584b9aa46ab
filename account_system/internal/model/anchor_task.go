package model

type TaskList struct {
	TaskID            int64  `gorm:"column:task_id;primary_key" json:"task_id"`
	TaskName          string `gorm:"column:task_name" json:"task_name"`
	TaskNameI18n      string `gorm:"column:task_name_i18n" json:"task_name_i18n"`
	ShowStartTime     int64  `gorm:"column:show_start_time" json:"show_start_time"`
	ShowStartDatetime string `gorm:"column:show_start_datetime" json:"show_start_datetime"`
	ShowEndTime       int64  `gorm:"column:show_end_time" json:"show_end_time"`
	ShowEndDatetime   string `gorm:"column:show_end_datetime" json:"show_end_datetime"`
	StartTime         int64  `gorm:"column:start_time" json:"start_time"`
	StartDatetime     string `gorm:"column:start_datetime" json:"start_datetime"`
	EndTime           int64  `gorm:"column:end_time" json:"end_time"`
	EndDatetime       string `gorm:"column:end_datetime" json:"end_datetime"`
	Sort              int64  `gorm:"column:sort" json:"sort"`                       // 排序，数字越大越靠前
	UserType          int64  `gorm:"column:user_type" json:"user_type"`             // 1为所有公会主播,2为指定用户
	WhiteMids         string `gorm:"column:white_mids" json:"white_mids"`           // 指定用户
	AreaRegions       string `gorm:"column:area_regions" json:"area_regions"`       // 内容大区，CS：华语区，ES：英语区
	TaskLabel         string `gorm:"column:task_label" json:"task_label"`           // 任务标签
	TaskLabelI118n    string `gorm:"column:task_label_i18n" json:"task_label_i18n"` // 任务标签
	Cost              int64  `gorm:"column:cost" json:"cost"`                       // 成本预估
	CostWarn          int64  `gorm:"column:cost_warn" json:"cost_warn"`             // 成本告警
	AuditStatus       int64  `gorm:"column:audit_status" json:"audit_status"`       // 0为未提交审核,1为提交审核中,2为审核通过,3为审核不通过
	IsOffline         int64  `gorm:"column:is_offline" json:"is_offline"`           // 1为下线
	Creator           string `gorm:"column:creator" json:"creator"`                 // 创建人
	LastEditor        string `gorm:"column:last_editor" json:"last_editor"`         // 最近一次编辑人
	Auditor           string `gorm:"column:auditor" json:"auditor"`                 // 审核人
	CreateDateTime    string `gorm:"column:create_datetime" json:"create_datetime"` // 创建时间
	UpdateDatetime    string `gorm:"column:update_datetime" json:"update_datetime"` // 修改时间
	AuditDatetime     string `gorm:"column:audit_datetime" json:"audit_datetime"`   // 审核时间
	AwardDatetime     string `gorm:"column:award_datetime" json:"award_datetime"`   // 发奖励的时间
}

func (*TaskList) TableName() string {
	return "anchor_task.task_list"
}

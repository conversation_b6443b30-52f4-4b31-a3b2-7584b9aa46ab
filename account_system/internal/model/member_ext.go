package model

import (
	"database/sql"
	"time"

	"github.com/shopspring/decimal"
)

type MemberExt struct {
	ID              int64  `bson:"_id"`               // 用户ID
	AccountLabel    int32  `bson:"account_label"`     // 大小号
	AccountLabelDid string `bson:"account_label_did"` // 大小号ID标识
	AreaRegion      string `bson:"area_region"`       // 用户大区
}

type MemberExtV2 struct {
	ID              int64  `bson:"_id"`               // 用户ID
	AccountLabel    int32  `bson:"account_label"`     // 大小号
	AccountLabelDid string `bson:"account_label_did"` // 大小号ID标识
	AreaRegion      string `bson:"area_region"`       // 用户大区
	Dt              int64  `bson:"dt"`                // 设备类型
	AppVer          string `bson:"app_ver"`           // 设备的类型
	Model           string `bson:"model"`             // 设备型号
}

type UserAreaList struct {
	ID             int64     `gorm:"column:id;primary_key" json:"id"`
	Mid            int64     `gorm:"column:mid" json:"mid"`
	Area           string    `gorm:"column:area" json:"area"`
	UpdateDatetime time.Time `gorm:"column:update_datetime" json:"update_datetime"`
	Operator       string    `gorm:"column:operator" json:"operator"`
}

func (*UserAreaList) TableName() string {
	return "cn_me_account.user_area_list"
}

type UserCountryList struct {
	ID             int64     `gorm:"column:id;primary_key" json:"id"`
	Mid            int64     `gorm:"column:mid" json:"mid"`
	Country        string    `gorm:"column:country" json:"country"`
	UpdateDatetime time.Time `gorm:"column:update_datetime" json:"update_datetime"`
	Operator       string    `gorm:"column:operator" json:"operator"`
}

func (*UserCountryList) TableName() string {
	return "cn_me_account.user_country_list"
}

const (
	// 1美金与金币积分的转换比例
	USD_TO_COIN_POINT_RATE = 100000
	// 金币与金币积分的转换比例
	COIN_TO_COIN_POINT_RATE = 1000
	// 英语币与金币积分的转换比例
	// E_COIN_TO_COIN_POINT_RATE = 1000
	E_COIN_TO_COIN_POINT_RATE = 10
)

type UserTradeDetail struct {
	ID           string    `json:"id" gorm:"column:id"`                   //唯一ID. 使用GenstrId产生
	Mid          int64     `json:"mid" gorm:"column:mid"`                 //账户mid
	Count        int64     `json:"count" gorm:"column:count"`             //货币变化数量
	CurrencyType string    `json:"type" gorm:"column:type"`               //货币类型
	From         string    `json:"from" gorm:"column:from"`               //send_gift 业务来源
	AreaRegion   string    `json:"area_region" gorm:"column:area_region"` //大区
	ExtId        string    `json:"ext_id" gorm:"column:ext_id"`           //外部ID
	ExtInfo      string    `json:"ext_info" gorm:"column:ext_info"`       //额外信息
	SdkApp       string    `json:"sdk_app" gorm:"column:sdk_app"`         // sdk还是主客户端
	RoomID       int64     `json:"room_id" gorm:"column:room_id"`         // 房间ID
	Ct           time.Time `json:"ct" gorm:"column:ct"`                   //时间
	Point        int64     `json:"point" gorm:"column:point"`             // 积分变化数量
}

func (*UserTradeDetail) TableName() string {
	return "me_trade_detail.user_trade_detail"
}

type UserTradeInfo struct {
	Id                   int64     `json:"id" gorm:"column:id"`                       // 用户id
	Coins                uint64    `json:"coins" gorm:"column:coins"`                 // 金币
	Tickets              uint64    `json:"tickets" gorm:"column:tickets"`             // 钻石
	CoinPoints           uint64    `json:"coin_points" gorm:"column:coin_points"`     // 金币积分数
	TicketPoints         uint64    `json:"ticket_points" gorm:"column:ticket_points"` // 钻石积分数
	Ct                   time.Time `json:"ct" gorm:"column:ct"`
	EnableTicketsToCoins bool      `json:"enable_tickets_to_coins" gorm:"column:enable_tickets_to_coins"`
}

func (*UserTradeInfo) TableName() string {
	return "me_trade_info.user_trade_info"
}

// Agent 代理信息
type Agent struct {
	Mid               int64           `gorm:"column:mid;primary_key"`
	CurrentCoins      decimal.Decimal `gorm:"column:current_coins;default:0;NOT NULL"`       // 当前金币数
	CurrentCoinPoints decimal.Decimal `gorm:"column:current_coin_points;default:0;NOT NULL"` // 当前金币积分数
}

func (*Agent) TableName() string {
	return "agent.agent"
}

// ChannelTradeInfo 渠道币表
type ChannelTradeInfo struct {
	Mid               int64 `json:"mid" gorm:"column:mid"`                                 //mid
	ChannelCoins      int64 `json:"channel_coins" gorm:"column:channel_coins"`             //渠道币
	ChannelCoinPoints int64 `json:"channel_coin_points" gorm:"column:channel_coin_points"` //渠道积分币
	Status            int64 `json:"status" gorm:"column:status"`                           //状态：1进行中，2已失效
	CreatedTime       int64 `gorm:"column:created_time"`
	UpdatedTime       int64 `gorm:"column:updated_time"`
}

func (*ChannelTradeInfo) TableName() string {
	return "me_trade_info.channel_trade_info"
}

// NobleTradeInfo 贵族币表
type NobleTradeInfo struct {
	Mid             int64        `json:"mid" gorm:"column:mid"`                             //mid
	NobleCoins      int64        `json:"noble_coins" gorm:"column:noble_coins"`             //货币变化数量
	NobleCoinPoints int64        `json:"noble_coin_points" gorm:"column:noble_coin_points"` // 贵族币积分
	Status          int64        `json:"status" gorm:"column:status"`                       //状态：1进行中，2已失效
	ExpiredAt       int64        `json:"expired_at" gorm:"column:expired_at"`               //状态：过期时间
	CreatedTime     time.Time    `json:"-" gorm:"column:created_time"`                      //创建时间
	UpdatedTime     sql.NullTime `json:"-" gorm:"column:updated_time"`                      //更新时间
}

func (*NobleTradeInfo) TableName() string {
	return "me_trade_info.noble_trade_info"
}

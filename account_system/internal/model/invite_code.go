package model

// RegisterCode 邀请码配置
type RegisterCode struct {
	Mid       int64  `json:"mid"`                  // 邀请码所属的玩家
	Type      string `json:"type"`                 // cms后台配置的,member 玩家生产的
	Code      string `json:"code"`                 // 邀请码,不能重复
	MaxCount  int64  `json:"max_count"`            // 最大邀请人数,负数则代表不限制
	Month     int64  `json:"month"`                // 所属月份
	ExpiredAt int64  `json:"expired_at"`           // 过期时间
	Operator  string `json:"operator" gorm:"null"` // 操作人
}

// TableName return register_code
func (r *RegisterCode) TableName() string {
	return "me_user_rank.register_code"
}

// RegisterCodeRelation 邀请码对应关系
type RegisterCodeRelation struct {
	Mid            int64 `json:"mid"`              // 用户
	InvitedMid     int64 `json:"invited_mid"`      // 邀请用户的mid
	RegisterCodeId int64 `json:"register_code_id"` // 邀请码流水号
}

// TableName return register_code
func (r *RegisterCodeRelation) TableName() string {
	return "me_user_rank.register_code_relation"
}

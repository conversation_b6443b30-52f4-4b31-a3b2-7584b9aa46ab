package model

type MidOnMicTime struct {
	MID         int64  `gorm:"column:mid"`
	Date        string `gorm:"column:date"`
	Role        int32  `gorm:"column:role"`
	TotalOnTime int64  `gorm:"column:total_on_time"`
}

func (r MidOnMicTime) TableName() string {
	return "data_center.room_mid_mic_heartbeat"
}

type KindRoom struct {
	RoomID   int64 `gorm:"column:room_id"`
	Status   int64 `gorm:"column:status"`
	Category int64 `gorm:"column:category"`
}

func (k KindRoom) TableName() string {
	return "me_room.kind_room"
}

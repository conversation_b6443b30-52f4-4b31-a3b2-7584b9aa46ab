package model

import (
	"time"
)

// DynamicLevelRecord 动态分级记录
type DynamicLevelRecord struct {
	ID            int64     `json:"id" gorm:"primaryKey;autoIncrement"`
	HDid          string    `json:"h_did" gorm:"column:h_did;type:varchar(128);not null;comment:设备唯一标识符;index:idx_h_did"`
	HDt           int32     `json:"h_dt" gorm:"column:h_dt;type:int;not null;comment:操作系统类型（0:Android 1:iOS）"`
	HOs           string    `json:"h_os" gorm:"column:h_os;type:varchar(32);not null;comment:操作系统版本号"`
	HModel        string    `json:"h_model" gorm:"column:h_model;type:varchar(128);comment:设备型号"`
	HAppid        string    `json:"h_appid" gorm:"column:h_appid;type:varchar(32);comment:应用版本唯一标识"`
	HAv           string    `json:"h_av" gorm:"column:h_av;type:varchar(32);comment:版本号"`
	HM            int64     `json:"h_m" gorm:"column:h_m;type:bigint;comment:用户ID"`
	HCh           string    `json:"h_ch" gorm:"column:h_ch;type:varchar(32);comment:渠道"`
	HCarrier      string    `json:"h_carrier" gorm:"column:h_carrier;type:varchar(32);comment:sim卡"`
	HAreaRegion   string    `json:"h_area_region" gorm:"column:h_area_region;type:varchar(32);comment:大区"`
	RoomId        int32     `json:"room_id" gorm:"column:room_id;type:int;comment:房间ID"`
	Scene         string    `json:"scene" gorm:"column:scene;type:varchar(32);not null;comment:场景"`
	DtType        int32     `json:"dt_type" gorm:"column:dt_type;type:int;not null;comment:端类型"`
	NetType       string    `json:"net_type" gorm:"column:net_type;type:varchar(16);comment:网络类型"`
	NetLatency    string    `json:"net_latency" gorm:"column:net_latency;type:text;comment:网络延迟数据"`
	NetThroughput string    `json:"net_throughput" gorm:"column:net_throughput;type:text;comment:网络带宽数据"`
	QuotaScores   string    `json:"quota_scores" gorm:"column:quota_scores;type:text;comment:各项指标详细分数"`
	IsRelegate    bool      `json:"is_relegate" gorm:"column:is_relegate;type:tinyint(1);not null;comment:是否降级"`
	Score         int32     `json:"score" gorm:"column:score;type:int;comment:综合评分结果"`
	NetSpeedLevel int32     `json:"net_speed_level" gorm:"column:net_speed_level;type:int;comment:网速等级"`
	UpdateTime    time.Time `json:"update_time" gorm:"column:update_time;type:datetime;not null;comment:记录更新时间"`
	CreateTime    time.Time `json:"create_time" gorm:"column:create_time;type:datetime;not null;default:CURRENT_TIMESTAMP;comment:记录创建时间"`
}

// TableName 指定表名
func (DynamicLevelRecord) TableName() string {
	return "cn_me_account.dynamic_level_record"
}

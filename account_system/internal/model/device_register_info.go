package model

import (
	"fmt"
	"time"
)

type DeviceRegisterInfo struct {
	ID           int64     `gorm:"column:id;primary_key"`
	HDid         string    `gorm:"column:h_did"`
	Mid          int64     `gorm:"column:mid"`
	Salt         string    `gorm:"column:salt"`
	HashPassword string    `gorm:"column:hash_password"`
	CreatedAt    time.Time `gorm:"column:created_at"`
	UpdatedAt    time.Time `gorm:"column:updated_at"`
}

func (d *DeviceRegisterInfo) TableName() string {
	return fmt.Sprintf("cn_me_account.device_register_info")
}

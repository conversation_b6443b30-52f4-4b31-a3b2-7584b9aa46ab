package action

import (
	"github.com/sirupsen/logrus"
	"gitlab.xunlei.cn/hiya/xc_server/xclib/common/xcaction"
	"gitlab.xunlei.cn/hiya/xc_server/xclib/common/xcaction/cfgstruct"
	"new-gitlab.xunlei.cn/funup/account_system/internal/config"
	"strconv"
)

func init() {
	xcActionConfig := config.XcActionConfig
	_ = xcaction.InitV2(cfgstruct.XcActionSt{
		Hostport: xcActionConfig.Address,
		Poolsize: xcActionConfig.PoolSize,
		Category: xcActionConfig.Category,
		Enable:   xcActionConfig.Enable,
	})
}

// AddActionLogV2 新增埋点，会上报到数数
func AddActionLogV2(mid int64, action *Action, opt map[string]interface{}) {
	defer func() {
		if iRec := recover(); iRec != nil {
			logrus.Errorf("AddActionLogV2 panic. error: %s", iRec)
		}
	}()

	if opt == nil {
		opt = map[string]interface{}{}
	}
	opt["h_m"] = mid
	err := xcaction.AddActionLog(
		strconv.FormatInt(mid, 10),
		action.Action,
		action.Otype,
		"0",
		"0",
		"hiya",
		opt)
	if err != nil {
		logrus.Errorf("add action log failed. error: %s", err)
		return
	}
}

type Action struct {
	Action string `json:"action"`
	Otype  string `json:"otype"`
}

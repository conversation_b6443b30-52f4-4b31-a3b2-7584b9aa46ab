package account_system_grpc

import (
	"fmt"
	"github.com/sirupsen/logrus"
	"google.golang.org/grpc"
	"new-gitlab.xunlei.cn/funup/yaplay_proto/account_system"
	"os"
	"zlutils/yaplay/grpc_client_util"
)

var (
	grpcUrl              string
	grpcConn             *grpc.ClientConn
	accountServiceClient account_system.AccountServiceClient
)

func Init() {
	env := os.Getenv("stage")
	if env == `dev` || env == "" {
		grpcUrl = fmt.Sprintf("consul://consul-prod.imfunup.com:8500/hiya_%s-account_system", env)
	} else if env == `test` {
		grpcUrl = fmt.Sprintf("consul://consul-prod.imfunup.com:8500/hiya_%s-account_system", env)
	} else {
		grpcUrl = fmt.Sprintf("consul://consul-prod.imfunup.com:8500/hiya_%s-account_system", env)
	}
	var err error
	grpcConn, err = grpc_client_util.InitGrpcConn(grpcUrl)

	if err != nil {
		logrus.WithError(err).WithFields(logrus.Fields{
			`grpcUrl`: grpcUrl,
		}).Panic("init grpc conn failed.")
	}
	grpcConn = getGrpcConn()
	accountServiceClient = account_system.NewAccountServiceClient(grpcConn)
	logrus.Errorf("success account")
}

func getGrpcConn() *grpc.ClientConn {
	return grpcConn
}

func GetAccountServiceClient() account_system.AccountServiceClient {
	return accountServiceClient
}

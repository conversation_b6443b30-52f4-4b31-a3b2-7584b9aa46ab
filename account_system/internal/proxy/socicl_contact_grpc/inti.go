package socicl_contact_grpc

import (
	"fmt"
	"github.com/sirupsen/logrus"
	"google.golang.org/grpc"
	family "new-gitlab.xunlei.cn/funup/yaplay_proto/social_contact"
	"os"
	"zlutils/yaplay/grpc_client_util"
)

var (
	GrpcUrl string

	grpcConn                   *grpc.ClientConn
	newFamilyBaseServiceClient family.FamilyBaseServiceClient
)

func Init() {
	env := os.Getenv("stage")
	if env == `dev` || env == "" {
		GrpcUrl = fmt.Sprintf("consul://consul-prod.imfunup.com:8500/hiya_%s-social_contact", env)
	} else if env == `test` {
		GrpcUrl = fmt.Sprintf("consul://consul-prod.imfunup.com:8500/hiya_%s-social_contact", env)
	} else {
		GrpcUrl = fmt.Sprintf("consul://consul-prod.imfunup.com:8500/hiya_%s-social_contact", env)
	}
	var err error
	grpcConn, err = grpc_client_util.InitGrpcConn(GrpcUrl)

	if err != nil {
		logrus.WithError(err).WithFields(logrus.Fields{
			`grpcUrl`: GrpcUrl,
		}).Errorf("init grpc conn failed.")
	}
	grpcConn = getGrpcConn()
	newFamilyBaseServiceClient = family.NewFamilyBaseServiceClient(grpcConn)
}

func getGrpcConn() *grpc.ClientConn {
	return grpcConn
}

func GetFamilyBaseServiceClient() family.FamilyBaseServiceClient {
	return newFamilyBaseServiceClient
}

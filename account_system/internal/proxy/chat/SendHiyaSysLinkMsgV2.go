package chat

import (
	"github.com/pkg/errors"
	"new-gitlab.xunlei.cn/funup/yaplay_proto/chat"
	"zlutils/yaplay/api_client"
	json "zlutils/yaplay/fast_json"
)

// SendHiyaSysLinkMsgV2 send hiya sys link msg v2
func (c *Client) SendHiyaSysLinkMsgV2(msgObj *chat.HiyaSysLinkMsgV2) (data *chat.SendMsgRespData, err error) {
	if msgObj == nil || msgObj.ToMid == 0 || msgObj.FromMid == 0 {
		return
	}

	msg := map[string]interface{}{
		"img": map[string]interface{}{
			"url": msgObj.ImageUrl,
			"h":   msgObj.ImageH,
			"w":   msgObj.ImageW,
		},
		"link":      msgObj.LinkUrl,
		"title":     msgObj.Title,
		"msg":       msgObj.Content,
		"operation": msgObj.Operation,
		"type":      msgObj.Type,
	}

	buf, err := json.Marshal(msg)
	if err != nil {
		return
	}
	param := &chat.SendMsgParam{
		Type:  "chat",
		Token: "yyyyyyyyyyyyyyyyyyyy1000",
		Data: &chat.SendMsgParam_Data{
			Localid:  msgObj.Uuid,
			Content:  string(buf),
			Mtype:    1003,
			Mtype2:   0,
			Fromuser: msgObj.FromMid,
			Touser:   msgObj.ToMid,
			HApp:     msgObj.AppName,
			Opt:      &chat.ChatSayOptParam{Push: true},
			PushContent: &chat.ChatSayPushContentParam{
				Body: msgObj.Content,
			},
		},
	}

	resp := &struct {
		api_client.ApiCode
		*chat.SendMsgRespData
	}{}
	err = c.apiClient.DoPost("/chat/httpapi/say", param, resp)
	if err != nil {
		err = errors.Wrap(err, "proxy.chat.SendHiyaSysLinkMsgV2, chat/httpapi/say failed")
		return
	}

	if resp.Error() != nil {
		return data, errors.Wrap(resp.Error(), "proxy.chat.SendHiyaSysLinkMsgV2")
	}

	data = resp.SendMsgRespData

	return
}

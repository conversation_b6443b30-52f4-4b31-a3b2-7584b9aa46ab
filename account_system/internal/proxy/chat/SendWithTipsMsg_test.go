package chat

import (
	"context"
	"github.com/stretchr/testify/assert"
	"testing"
)

func TestClient_SendWithTipsMsg(t *testing.T) {
	chatApiClient := NewChatApiClient(context.Background(), "https://test-api.imfunup.com/test_inner")
	//https://img.helloteemo.com.cn/2022/05/11/1652240469.png

	data, err := chatApiClient.SendWithTipsMsg(
		5, 2000027, "我是策略信", "策略信")

	assert.Nil(t, err)

	t.Logf("%+v", data)
}

func TestClient_SendWavWithTipsMsg(t *testing.T) {
	chatApiClient := NewChatApiClient(context.Background(), "https://test-api.imfunup.com/test_inner")
	//https://img.helloteemo.com.cn/2022/05/11/1652240469.png

	data, err := chatApiClient.SendWavWithTipsMsg(
		2000027, 5, "mead/df/cd/8c09-d21f-11ed-b2c3-fe8e23dfa4a4", 5235, "策略信")

	assert.Nil(t, err)

	t.Logf("%+v", data)
}

func TestClient_SendImgWithTipsMsg(t *testing.T) {
	chatApiClient := NewChatApiClient(context.Background(), "https://test-api.imfunup.com/test_inner")
	//https://img.helloteemo.com.cn/2022/05/11/1652240469.png

	data, err := chatApiClient.SendImgWithTipsMsg(
		2000027, 5, 2304002565351, 1080, 1440,
		"a69582660e4c80d2db773bbd03b6873b", "策略信",
	)

	assert.Nil(t, err)

	t.Logf("%+v", data)
}

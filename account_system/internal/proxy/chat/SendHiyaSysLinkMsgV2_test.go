package chat

import (
	"context"
	"github.com/stretchr/testify/assert"
	"new-gitlab.xunlei.cn/funup/account_system/internal/proxy/hiya_scheme"
	"new-gitlab.xunlei.cn/funup/yaplay_proto/chat"
	"testing"
	"time"
)

func TestClient_SendHiyaSysLinkMsgV2(t *testing.T) {
	chatApiClient := NewChatApiClient(context.Background(), "https://test-api.imfunup.com/test_inner")
	now := time.Now()

	linkUrl := hiya_scheme.NewOpenUrlBuilder().MustUrl(`https://test-h5.imfunup.com/#/global/wallet`).
		FullScreen(1).
		CloseCurrent().Build()

	data, err := chatApiClient.SendHiyaSysLinkMsgV2(&chat.HiyaSysLinkMsgV2{
		FromMid:   5,
		ToMid:     2000027,
		Uuid:      now.Unix()*********** + now.UnixNano(),
		ImageUrl:  "https://img.helloteemo.com.cn/2022/05/11/**********.png",
		ImageH:    315,
		ImageW:    349,
		LinkUrl:   linkUrl,
		Title:     "订单充值完成,金币已到账",
		Content:   "充值ID: 20230207201902312408702997898001\n充值金币数: 1000",
		Operation: "GO",
		AppName:   "me-live",
		Type:      0,
	})

	assert.Nil(t, err)

	t.Logf("%+v", data)
}

package chat

import (
	"github.com/pkg/errors"
	"new-gitlab.xunlei.cn/funup/yaplay_proto/chat"
	"time"
	"zlutils/yaplay/api_client"
	json "zlutils/yaplay/fast_json"
)

// SendWithTipsMsg send with tips msg
func (c *Client) SendWithTipsMsg(fromMid, mid int64, content, tips string) (data *chat.SendMsgRespData, err error) {
	if mid == 0 || fromMid == 0 {
		return
	}

	now := time.Now()
	localID := now.Unix()*1000000000 + now.UnixNano()

	msg := map[string]interface{}{
		"content": content,
		"tips":    tips,
	}

	buf, err := json.Marshal(msg)
	if err != nil {
		return
	}
	param := &chat.SendMsgParam{
		Type:  "chat",
		Token: "yyyyyyyyyyyyyyyyyyyy1000",
		Data: &chat.SendMsgParam_Data{
			Localid:  localID,
			Content:  string(buf),
			Mtype:    1011,
			Mtype2:   101, // 策略信消息
			Fromuser: fromMid,
			Touser:   mid,
			HApp:     "me-live",
			Opt:      &chat.ChatSayOptParam{Push: true},
			PushContent: &chat.ChatSayPushContentParam{
				Body: content,
			},
			IsHideSender: false,
		},
	}

	resp := &struct {
		api_client.ApiCode
		Data *chat.SendMsgRespData `json:"data"`
	}{}
	err = c.apiClient.DoPost("/chat/httpapi/say", param, resp)
	if err != nil {
		err = errors.Wrap(err, "proxy.chat.SendHiyaSysLinkMsgV2, chat/httpapi/say failed")
		return
	}

	if resp.Error() != nil {
		return data, errors.Wrap(resp.Error(), "proxy.chat.SendHiyaSysLinkMsgV2")
	}

	data = resp.Data

	//sessionID := resp.Data.GetSessionId()
	//messageID := resp.Data.GetMsgid()
	//
	//hideParam := &chat.HideSessionParam{
	//	Mid:         fromMid,
	//	SessionId:   sessionID,
	//	Msgid:       messageID,
	//	SessionType: 1,
	//	HApp:        "me-live",
	//}
	//err = c.HideSession(hideParam)
	//if err != nil {
	//	return data, err
	//}

	return
}

// SendWavWithTipsMsg send wav with tips msg
func (c *Client) SendWavWithTipsMsg(fromMid, mid int64, wavUrl string, duration int64, tips string) (
	data *chat.SendMsgRespData, err error) {

	if mid == 0 || fromMid == 0 {
		return
	}

	now := time.Now()
	localID := now.Unix()*1000000000 + now.UnixNano()

	msg := map[string]interface{}{
		"fmt":      "wav",
		"uri":      wavUrl,
		"duration": duration,
		"tips":     tips,
	}

	buf, err := json.Marshal(msg)
	if err != nil {
		return
	}
	param := &chat.SendMsgParam{
		Type:  "chat",
		Token: "yyyyyyyyyyyyyyyyyyyy1000",
		Data: &chat.SendMsgParam_Data{
			Localid:     localID,
			Content:     string(buf),
			Mtype:       1012, // 语音消息
			Mtype2:      101,  // 策略信消息
			Fromuser:    fromMid,
			Touser:      mid,
			HApp:        "me-live",
			Opt:         &chat.ChatSayOptParam{Push: true},
			PushContent: &chat.ChatSayPushContentParam{
				//Body: content,
			},
			IsHideSender: false,
		},
	}

	resp := &struct {
		api_client.ApiCode
		Data *chat.SendMsgRespData `json:"data"`
	}{}
	err = c.apiClient.DoPost("/chat/httpapi/say", param, resp)
	if err != nil {
		err = errors.Wrap(err, "proxy.chat.SendHiyaSysLinkMsgV2, chat/httpapi/say failed")
		return
	}

	if resp.Error() != nil {
		return data, errors.Wrap(resp.Error(), "proxy.chat.SendHiyaSysLinkMsgV2")
	}

	data = resp.Data

	sessionID := resp.Data.GetSessionId()
	messageID := resp.Data.GetMsgid()

	hideParam := &chat.HideSessionParam{
		Mid:         fromMid,
		SessionId:   sessionID,
		Msgid:       messageID,
		SessionType: 1,
		HApp:        "me-live",
	}
	err = c.HideSession(hideParam)
	if err != nil {
		return data, err
	}

	return
}

// SendImgWithTipsMsg send img with tips msg
func (c *Client) SendImgWithTipsMsg(fromMid, mid int64, imgId int64, w, h int64, md5, tips string) (
	data *chat.SendMsgRespData, err error) {

	if mid == 0 || fromMid == 0 {
		return
	}

	now := time.Now()
	localID := now.Unix()*1000000000 + now.UnixNano()

	msg := map[string]interface{}{
		"fmt":  "jpeg",
		"id":   imgId,
		"md5":  md5,
		"w":    w,
		"h":    h,
		"tips": tips,
	}

	buf, err := json.Marshal(msg)
	if err != nil {
		return
	}
	param := &chat.SendMsgParam{
		Type:  "chat",
		Token: "yyyyyyyyyyyyyyyyyyyy1000",
		Data: &chat.SendMsgParam_Data{
			Localid:     localID,
			Content:     string(buf),
			Mtype:       2,   // 图片
			Mtype2:      101, // 策略信消息
			Fromuser:    fromMid,
			Touser:      mid,
			HApp:        "me-live",
			Opt:         &chat.ChatSayOptParam{Push: true},
			PushContent: &chat.ChatSayPushContentParam{
				//Body: content,
			},
			IsHideSender: false,
		},
	}

	resp := &struct {
		api_client.ApiCode
		Data *chat.SendMsgRespData `json:"data"`
	}{}
	err = c.apiClient.DoPost("/chat/httpapi/say", param, resp)
	if err != nil {
		err = errors.Wrap(err, "proxy.chat.SendHiyaSysLinkMsgV2, chat/httpapi/say failed")
		return
	}

	if resp.Error() != nil {
		return data, errors.Wrap(resp.Error(), "proxy.chat.SendHiyaSysLinkMsgV2")
	}

	data = resp.Data

	sessionID := resp.Data.GetSessionId()
	messageID := resp.Data.GetMsgid()

	hideParam := &chat.HideSessionParam{
		Mid:         fromMid,
		SessionId:   sessionID,
		Msgid:       messageID,
		SessionType: 1,
		HApp:        "me-live",
	}
	err = c.HideSession(hideParam)
	if err != nil {
		return data, err
	}

	return
}

// HideSession 隐藏某个session
func (c *Client) HideSession(param *chat.HideSessionParam) (err error) {
	resp := &api_client.ApiCode{}
	err = c.apiClient.DoPost("/chat/httpapi/hide_session", param, resp)
	if err != nil {
		err = errors.Wrap(err, "proxy.chat.HideSession, chat/httpapi/hide_session failed")
		return
	}

	if resp.Error() != nil {
		return errors.Wrap(resp.Error(), "proxy.chat.HideSession")
	}
	return
}

package account

import (
	"github.com/sirupsen/logrus"
	"strings"
)

// IsMainlandMember 判断是否是内地用户
func (a *Account) IsMainlandMember(mid int64) (isMainlandMember bool, country string, err error) {
	var respData struct {
		Data struct {
			IsMainland bool   `json:"is_mainland"`
			Country    string `json:"country"`
		} `json:"data"`
	}

	uri := "/rpc/account_v2/account/is_mainland"

	var param = struct {
		Mid int64 `json:"mid"`
	}{Mid: mid}
	err = a.apiClient.DoPost(uri, param, &respData)
	if err != nil {
		logrus.WithField("req", mid).Printf("IsMainlandMember err:%s", err.Error())
		return
	}
	country = strings.ToLower(respData.Data.Country)

	isMainlandMember = respData.Data.IsMainland && country == "cn"

	return
}

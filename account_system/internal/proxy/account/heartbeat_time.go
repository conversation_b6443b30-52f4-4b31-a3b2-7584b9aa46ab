package account

import (
	"github.com/pkg/errors"
	"time"
	"zlutils/yaplay/api_client"
)

// GetHeartbeatOnlineSign 获取用户在线状态
func GetHeartbeatOnlineSign(unix int64) string {
	now := time.Now().Unix()
	// 在线、5mins内活跃、2h内活跃、24h内活跃、3天内活跃、7天内活跃、7天前活跃；
	// 1、在线：now - unix < 5mins
	if now-unix < 5*60 {
		return "在线"
	}
	// 2、5mins内活跃：5mins < now - unix < 2h
	if now-unix < 2*60*60 {
		return "5分钟内活跃"
	}
	// 3、2h内活跃：2h < now - unix < 24h
	if now-unix < 24*60*60 {
		return "2小时内活跃"
	}
	// 4、24h内活跃：24h < now - unix < 3天
	if now-unix < 3*24*60*60 {
		return "24小时内活跃"
	}
	// 5、3天内活跃：3天 < now - unix < 7天
	if now-unix < 7*24*60*60 {
		return "3天内活跃"
	}
	// 6、7天内活跃：7天 < now - unix < 7天前
	if now-unix < 7*24*60*60 {
		return "7天内活跃"
	}
	// 7、7天前活跃：now - unix > 7天
	return "7天前活跃"
}

// IsOnlineByLastHeartbeat 根据最后心跳时间判断用户是否在线
func IsOnlineByLastHeartbeat(heartbeatTime int64) bool {
	return time.Now().Unix()-heartbeatTime < 5*60
}

// GetUserLastHeartbeat 获取用户最后心跳时间
func (a *Account) GetUserLastHeartbeat(midList []int64) (map[int64]int64, error) {
	res := &struct {
		api_client.ApiCode
		Data map[int64]int64 `json:"data"`
	}{}
	params := map[string]interface{}{
		`mid_list`: midList,
	}
	if err := a.apiClient.DoPost(`/rpc/account_v2/users/last_heartbeat`, params, res); err != nil {
		err = errors.Wrap(err, "heartbeat.GetUserLastHeartbeat")
		return nil, err
	}
	if res.Error() != nil {
		err := errors.Wrap(res.Error(), "heartbeat.GetUserLastHeartbeat res.Error")
		return nil, err
	}
	return res.Data, nil
}

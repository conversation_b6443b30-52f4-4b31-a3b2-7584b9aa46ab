package account

import (
	"context"
	"github.com/stretchr/testify/assert"
	"testing"
)

func TestAccountApi_GetUserLastHeartbeat(t *testing.T) {
	urlPrefix := "https://test-api.imfunup.com"
	api := NewAccount(context.Background(), urlPrefix)
	heartbeat, err := api.GetUserLastHeartbeat([]int64{2000027, 2000032})
	assert.Nil(t, err)
	for mid, ti := range heartbeat {
		t.Logf("mid: %v, ti: %v", mid, ti)
	}
}

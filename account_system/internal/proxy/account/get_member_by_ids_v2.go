package account

import (
	"gitlab.xunlei.cn/hiya/xc_server/xclib/common/xcproto"
	"new-gitlab.xunlei.cn/funup/yaplay_proto/account"
	"time"
	"zlutils/yaplay/api_client"
)

// GetMemberByIdsV2 获取房间信息
func (a *Account) GetMemberByIdsV2(list []int64) (resp account.GetMemberByIdsV2Data, err error) {
	uri := "/account/httpapi/get_member_by_ids_v2"
	respData := &struct {
		api_client.ApiCode
		Data account.GetMemberByIdsV2Data `json:"data"`
	}{}

	param := account.GetMemberByIdsV2Param{
		Ids: list,
	}

	err = a.apiClient.DoPost(uri, param, respData)
	if err != nil {
		return
	}

	err = respData.Error()
	if err != nil {
		return
	}
	resp = respData.Data
	return
}

type BlackMidKind int

const (
	BlackMidKindNone       BlackMidKind = iota // 0
	BlackMidKindWarn                           // 警告
	BlackMidKindDevice                         // 设备封禁
	BlackMidKindAccountBan                     // 账号封禁
	BlackMidKindIdCard                         // 身份证封禁
	BlackMidKindRoom                           // 禁止进入商业厅
)

type GetBlackMidParam struct {
	UUID     string       `json:"uuid,omitempty"`
	Kind     BlackMidKind `json:"kind,omitempty"`
	Deadline int64        `json:"deadline,omitempty"`
}

type BlackMidSt struct {
	ID        string       `json:"id" bson:"_id,omitempty"`
	UUID      string       `json:"uuid" bson:"uuid"`
	Kind      BlackMidKind `json:"kind" bson:"kind"`
	Deadline  int64        `json:"deadline" bson:"deadline"`
	CommentId int64        `json:"comment_id" bson:"comment_id"`
	Ct        int64        `json:"ct" bson:"ct"`
	OpId      int64        `json:"op_id,omitempty"`
	Mid       int64        `json:"mid"`
	SrcMid    int64        `json:"src_mid"`
}

type GetBlackMidResp struct {
	api_client.ApiCode
	Data *BlackMidSt `json:"data,omitempty"`
}

// GetBlackMid 获取黑产名单
func (a *Account) GetBlackMid(mid string, kind BlackMidKind) (rsp *GetBlackMidResp, err error) {
	uri := "/account/httpapi/get_black_mid"
	respData := &GetBlackMidResp{}
	param := GetBlackMidParam{
		UUID:     mid,
		Kind:     kind,
		Deadline: time.Now().Unix(),
	}
	err = a.apiClient.DoPost(uri, param, respData)
	if err != nil {
		return
	}
	err = respData.Error()
	if err != nil {
		return
	}
	rsp = respData
	return
}

type GetMemberByIdsParam struct {
	BaseParm
	Ids []int64 `json:"ids,omitempty"`
}

type GetMemberByIdsResult struct {
	Members map[int64]*xcproto.MemberSt `msgpack:"members" json:"members,omitempty"`
}

// GetMemberByIds 获取用户的信息
func (a *Account) GetMemberByIds(list []int64) (resp *GetMemberByIdsResult, err error) {
	uri := "/account/httpapi/get_member_by_ids"
	respData := &struct {
		api_client.ApiCode
		Data *GetMemberByIdsResult `json:"data"`
	}{}

	param := GetMemberByIdsParam{
		Ids: list,
	}

	err = a.apiClient.DoPost(uri, param, respData)
	if err != nil {
		return
	}

	err = respData.Error()
	if err != nil {
		return
	}
	resp = respData.Data
	return
}

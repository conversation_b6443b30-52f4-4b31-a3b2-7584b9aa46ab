package account

import (
	"context"
	"github.com/stretchr/testify/assert"
	"testing"
)

func TestAccount_GetMemberByIdsV2(t *testing.T) {
	account := NewAccount(context.Background(), "https://test-api-in.imfunup.com")

	memberByIdsV2, err := account.GetMemberByIdsV2([]int64{**********, 2000027, 5})
	assert.Nil(t, err)
	assert.Equal(t, 3, len(memberByIdsV2.Members))

	for _, st := range memberByIdsV2.Members {
		t.Logf("%+v", st)
	}
}

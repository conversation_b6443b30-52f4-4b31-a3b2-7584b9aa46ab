package account

import (
	"github.com/sirupsen/logrus"
	"gitlab.xunlei.cn/hiya/xc_server/xclib/common/xcproto"
	"zlutils/yaplay/api_client"
)

type AppPackage string

type LocSt struct {
	Lat      float64 `json:"h_lat"`    // 纬度
	Lon      float64 `json:"h_lon"`    // 经度
	Province string  `json:"province"` // 省份
	City     string  `json:"city"`     // 城市
}

// @see https://doc2.ixiaochuan.cn/pages/viewpage.action?pageId=2320385
type BaseParm struct {
	// NOTE:固定传me-live,chat服务对其做了深度定制，不好对其做修改
	App              string      `json:"h_app,omitempty"` // me-live
	AppVer           string      `json:"h_av"`            // app版本号
	HostVer          string      `json:"h_av_host"`       // 宿主app版本号，仅在sdk时有值
	DevType          int32       `json:"h_dt"`            // 操作系统 0安卓 1ios
	DevId            string      `json:"h_did"`           // 设备号
	NetType          int32       `json:"h_nt"`            // 网络类型
	MemId            int64       `json:"h_m,omitempty"`   // mid
	SdkHostMid       int64       `json:"h_mid,omitempty"` // 皮皮搞笑app调用hiya接口，皮皮搞笑mid传递在h_mid
	Loc              LocSt       `json:"h_loc,omitempty"` // 坐标
	Chan             string      `json:"h_ch,omitempty"`  // 下载渠道
	Timestamp        int64       `json:"h_ts"`            // 当前时间
	Model            string      `json:"h_model"`         // 手机型号
	Token            string      `json:"token"`           // token
	Ver              string      `json:"ver"`             // Deprecated
	ClientIP         string      `json:"client_ip"`
	Language         string      `json:"h_language,omitempty"` // 客户端使用语言 "en": 英语 "ar"：阿拉伯语
	Adjust           string      `json:"h_adjust"`             // Adjust标识
	Sys              string      `json:"h_sys"`                // Android上报android id, iOS上报idfv
	Gender           int         `json:"h_gender,omitempty"`   // 性别
	Age              int         `json:"h_age,omitempty"`      // 年龄
	Ip               string      `json:"h_ip,omitempty"`       // 客户端ip
	ZoneName         string      `json:"h_zone_name"`          // 用户时区的名称
	ZoneAbbreviation string      `json:"h_zone_abbreviation"`  // 用户时区的缩写
	ZoneOffset       int64       `json:"h_zone_offset"`        // 用户时区与零时区的间隔秒数
	RequestId        string      `json:"request_uuid"`         // 请求唯一id
	AppsFlyerId      string      `json:"h_appsflyerid"`        // AppsFlyer 标识
	ShuMeiId         string      `json:"h_shumei_id"`          // 数美设备标识
	HeaderRegionCode int64       `json:"h_region_code"`        // 国家代码
	AreaCode         int32       `json:"h_area_code"`          // 区域代码
	Carrier          string      `json:"h_carrier"`            // 运营商代码 2.5.0添加 示例：中国联通,cn,46001
	Test             interface{} `json:"h_test"`               // android int 0,1; ios bool
	SdkApp           string      `json:"h_sdk_app"`            // sdk宿主app类型。空或者hy: hiya；pp：皮皮搞笑
	AndroidId        string      `json:"h_android_id"`         // androidId
	IMEI             string      `json:"h_imei"`
	OAID             string      `json:"h_oaid"`
	IDFA             string      `json:"h_idfa"`
	Package          AppPackage  `json:"h_package"` // 应用包名. 旧版本hiya没传。hiya马甲包、皮皮搞笑会传
	CityCode         int64       `json:"h_city_code"`
	ProvinceCode     int64       `json:"h_province_code"`
	OsVersion        interface{} `json:"h_os"` // 操作系统版本, 因客户端上报类型不一致，改为interface

	// NOTE: 为了区分马甲包而新加的参数
	// 具体取值见 https://alidocs.dingtalk.com/i/nodes/Xzr6RBgD3LYJnMq13gq0VZPnyElvd7e9
	AppID string `json:"h_appid,omitempty"`
}

type InitInfoParam struct {
	BaseParm
	Mid               int64  `json:"mid,omitempty"`
	Name              string `json:"name,omitempty"`
	Avatar            int64  `json:"avatar,omitempty"`
	Gender            int64  `json:"gender,omitempty"`
	Birth             int64  `json:"birth,omitempty"`
	Country           int64  `json:"country,omitempty"`
	InvitationCode    string `json:"invitation_code"`
	IsDefault         int64  `json:"is_default"`
	IpAddress         string `json:"ip_address"`
	Location          int64  `json:"location"`
	IsChineseMainLand bool   `json:"is_chinese_main_land"`
}

type Member struct {
	xcproto.MemberSt
}

type InitInfoData struct {
	Member
	BottomTabHit         int         `json:"bottom_tab_hit"`
	BottomTabVoiceSwitch int         `json:"bottom_tab_voice_switch"`
	Message              string      `json:"message"`                   // 非空客户端弹提示
	AndroidChannelRoomID interface{} `json:"android_channel_room_info"` // 安卓渠道房间信息，具体的结构体是 room_proto.RoomInfoSt

	// 是否需要输入注册邀请码
	NeedEnterRegisterCode bool `json:"need_enter_register_code" msgpack:"need_enter_register_code,omitempty"`
}

// InitInfo 判断是否是内地用户
func (a *Account) InitInfo(param InitInfoParam) (*InitInfoData, error) {
	uri := "/account/httpapi/init_info"
	respData := &struct {
		api_client.ApiCode
		Data InitInfoData `json:"data"`
	}{}

	err := a.apiClient.DoPost(uri, param, respData)
	if err != nil {
		return nil, err
	}
	err = respData.Error()
	if err != nil {
		return nil, err
	}
	return &respData.Data, nil
}

type AddReviewAccountReq struct {
	Mid  int64  `json:"mid"`
	HDid string `json:"h_did"`
	HAv  string `json:"h_av"`
	HDt  int64  `json:"h_dt"`
}

// AddReviewAccount 添加进审核
func (a *Account) AddReviewAccount(param AddReviewAccountReq) error {
	uri := "/rpc/account_v2/account/add_review_account"
	respData := &struct {
		api_client.ApiCode
	}{}

	err := a.apiClient.DoPost(uri, param, respData)
	if err != nil {
		return err
	}
	err = respData.Error()
	if err != nil {
		return err
	}
	return nil
}

// GetUsersByLabelDids 根据设备获取所有相同注册设备id的用户
func (a *Account) GetUsersByLabelDids(dids []string) (map[string][]int64, error) {
	req := &GetUsersByLabelDidsReq{
		AccountLabelDids: dids,
	}

	respData := &GetUsersByLabelDidsResp{}
	accountLabelDidMap := make(map[string][]int64, 0)
	uri := "/rpc/account_v2/get_users_by_label_dids"
	err := a.apiClient.DoPost(uri, req, respData)
	if err != nil {
		logrus.Errorf(" GetUsersByLabelDidsresp reqData:%v,  error. %v", dids, err)
		return accountLabelDidMap, err
	}

	err = respData.Error()
	if err != nil || respData.Data.AccountLabelDidMap == nil {
		logrus.Errorf(" GetUsersByLabelDidsresp reqData:%v,  error. %v", dids, err)
		return accountLabelDidMap, err
	}
	accountLabelDidMap = respData.Data.AccountLabelDidMap
	return accountLabelDidMap, nil
}

type GetUsersByLabelDidsResp struct {
	api_client.ApiCode
	Data GetUsersByLabelDidsResult `json:"data"`
}

type GetUsersByLabelDidsResult struct {
	AccountLabelDidMap map[string][]int64 `json:"account_label_did_map"`
}

type GetUsersByLabelDidsReq struct {
	AccountLabelDids []string `json:"account_label_dids"`
}

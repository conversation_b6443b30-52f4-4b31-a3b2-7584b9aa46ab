package account

import (
	"context"
	"zlutils/yaplay/api_client"
)

// Account  rpc
type Account struct {
	ctx       context.Context
	apiClient api_client.ApiClient
}

// NewAccount msg dispatch
func NewAccount(ctx context.Context, urlPrefix string) *Account {
	return &Account{
		ctx: ctx,
		apiClient: api_client.ApiClient{
			UrlPrefix:  urlPrefix,
			Ctx:        ctx,
			HttpClient: api_client.ShortTimeoutRequestClient,
		},
	}
}

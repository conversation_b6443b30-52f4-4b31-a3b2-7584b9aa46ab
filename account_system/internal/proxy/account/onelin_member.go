package account

import "zlutils/yaplay/api_client"

// OnlineMember 判断在线
func (a *Account) OnlineMember(midList []int64) (onlineMember []int64, err error) {
	var respData struct {
		api_client.ApiCode
		Data []int64 `json:"data"`
	}

	uri := "/rpc/account_v2/users/online_member"

	var param = struct {
		MidList []int64 `json:"mid_list"`
	}{MidList: midList}
	err = a.apiClient.DoPost(uri, param, &respData)
	if err != nil {
		return
	}
	err = respData.Error()
	if err != nil {
		return nil, err
	}
	onlineMember = respData.Data
	return
}

package account

import (
	"github.com/sirupsen/logrus"
)

// GetIpsLocation 获取IP国家信息
func (a *Account) GetIpsLocation(ips []string) (countryMap map[string]string, err error) {
	var respData struct {
		Data struct {
			CountryMap map[string]string `json:"country_map"`
		} `json:"data"`
	}

	uri := "/rpc/account_v2/get_ips_location"

	var param = struct {
		Ips []string `json:"ips"`
	}{
		Ips: ips,
	}
	err = a.apiClient.DoPost(uri, param, &respData)
	if err != nil {
		logrus.WithField("ips", ips).Printf("GetIpsLocation err:%s", err.<PERSON>rror())
		return
	}
	countryMap = respData.Data.CountryMap
	return
}

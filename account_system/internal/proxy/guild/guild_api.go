package guild

import (
	"context"
	"github.com/sirupsen/logrus"
	"zlutils/yaplay/api_client"
)

// ApiClient api client
type ApiClient struct {
	api_client.ApiClient
}

// NewApiClient 新建一个api client
func NewApiClient(ctx context.Context, urlPrefix string) *ApiClient {
	return &ApiClient{
		ApiClient: api_client.ApiClient{
			UrlPrefix:  urlPrefix,
			Ctx:        ctx,
			HttpClient: api_client.ShortTimeoutRequestClient,
		},
	}
}

// GetGuildInfoByMidParams param
type GetGuildInfoByMidParams struct {
	Mid int64 `json:"mid" form:"mid"`
}

// GetGuildInfoByMidResponse resp
type GetGuildInfoByMidResponse struct {
	api_client.ApiCode
	Data GetGuildInfoByMidData `json:"data"`
}

// GetGuildInfoByMidData data
type GetGuildInfoByMidData struct {
	Info struct {
		Id    int64  `json:"id"`
		Name  string `json:"name"`
		Owner struct {
			Id int64 `json:"id"`
		} `json:"owner"`
		BillType BillType `json:"bill_type"` // 工会类型
		CreateTs int      `json:"create_ts"`
	} `json:"info"`
	Joined bool `json:"joined"`
}

// BillType 工会类型
type BillType int

const (
	// PackageSettlement 打包结算
	PackageSettlement BillType = iota + 1
	// NotPackageSettlement  非打包结算
	NotPackageSettlement
)

// GetGuildInfoByMid 获取我的工会信息
func (a *ApiClient) GetGuildInfoByMid(mid int64) (resp *GetGuildInfoByMidResponse, err error) {

	logger := logrus.WithContext(a.Ctx).WithFields(logrus.Fields{
		"func": "ApiClient.GetGuildInfoByMid",
	})

	resp = &GetGuildInfoByMidResponse{}
	req := &GetGuildInfoByMidParams{
		Mid: mid,
	}

	uri := "/rpc/v1/my/guild/info"
	err = a.DoGet(uri, req, resp)
	if err != nil {
		logger.Errorf("do request failed. %s", err)
		return
	}

	err = resp.Error()
	if err != nil {
		logger.Errorf("api return error. %s", err)
		return
	}
	return
}

// FindGuildInfoByGuildId 获取工会信息
func (a *ApiClient) FindGuildInfoByGuildId(guildId int64) (resp *GetGuildInfoByMidResponse, err error) {

	logger := logrus.WithContext(a.Ctx).WithFields(logrus.Fields{
		"func": "ApiClient.GetGuildInfoByMid",
	})

	resp = &GetGuildInfoByMidResponse{}
	req := &map[string]interface{}{
		`guild_id`: guildId,
	}

	uri := "/rpc/v1/guild/info"
	err = a.DoGet(uri, req, resp)
	if err != nil {
		logger.Errorf("do request failed. %s", err)
		return
	}

	err = resp.Error()
	if err != nil {
		logger.Errorf("api return error. %s", err)
		return
	}
	return
}

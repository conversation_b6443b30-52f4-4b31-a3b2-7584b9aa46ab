package guild

import (
	"context"
	"github.com/stretchr/testify/assert"
	"testing"
)

func TestApiClient_FindGuildInfoByGuildId(t *testing.T) {
	urlPrefix := "https://test-api.imfunup.com/hiya_guild"
	client := NewApiClient(context.Background(), urlPrefix)
	response, err := client.FindGuildInfoByGuildId(100315)
	assert.Nil(t, err)
	t.Logf("%+v", response)
}

func TestApiClient_GetGuildInfoByMid(t *testing.T) {
	urlPrefix := "https://test-api.imfunup.com/hiya_guild"
	client := NewApiClient(context.Background(), urlPrefix)
	mid, err := client.GetGuildInfoByMid(5)
	assert.Nil(t, err)
	t.Logf("%+v", mid)
}

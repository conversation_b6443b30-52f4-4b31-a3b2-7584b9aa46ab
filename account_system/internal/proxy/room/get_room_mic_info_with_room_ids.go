package room

import (
	"github.com/pkg/errors"
	"zlutils/yaplay/api_client"
)

// MicInfo mic info
type MicInfo struct {
	MicId          int    `json:"mic_id"`
	RoomId         int64  `json:"room_id"`
	Mid            int64  `json:"mid"`
	Status         int    `json:"status"`
	CloseMicStatus int    `json:"close_mic_status"`
	CloseMicSource string `json:"close_mic_source"`
	Pos            int    `json:"pos"`
	Ut             int    `json:"ut"`
}

// GetRoomMicInfoWithRoomIds 获取房间的mic信息
func (d *Client) GetRoomMicInfoWithRoomIds(roomIds []int64) (resMap map[int64][]*MicInfo, err error) {
	res := &struct {
		api_client.ApiCode
		Data struct {
			ResMap map[int64][]*MicInfo `json:"res_map"`
		} `json:"data"`
	}{}

	params := map[string]interface{}{
		`room_ids`: roomIds,
	}
	if err = d.apiClient.DoPost(`/room/httpapi/get_room_mic_info_with_room_ids`, params, res); err != nil {
		err = errors.Wrap(err, "room.GetRoomMicInfoWithRoomIds")
		return
	}
	if res.Error() != nil {
		err = errors.Wrap(res.Error(), "room.GetRoomMicInfoWithRoomIds res.Error")
		return
	}
	resMap = res.Data.ResMap
	return
}

package room

import "zlutils/yaplay/api_client"

type GetRoomWithRoomIdsData struct {
	List []*RoomInfoSt         `json:"list"`
	Map  map[int64]*RoomInfoSt `json:"map"`
}

// StatusTy 房间状态
type StatusTy = int32

const (
	// NoLiving 未开播
	NoLiving StatusTy = 0
	// IsLiving 已开播
	IsLiving StatusTy = 1
	// IsBlocked 直播间被封禁
	IsBlocked StatusTy = 2
)

// RoomInfoSt 直播间信息
type RoomInfoSt struct {
	RoomId              int64        `json:"room_id"`
	RoomCornerMark      string       `json:"room_corner_mark"`
	Mid                 int64        `json:"mid"`
	AnchorMid           int64        `json:"anchor_mid"`
	UnionId             int64        `json:"union_id"`
	Ct                  int64        `json:"ct"`
	Ut                  int64        `json:"ut"`
	RoomScore           int64        `json:"room_score"`
	Status              StatusTy     `json:"status"`       // 房间状态：0：未开播，1：已开播，2：直播间被封禁'
	OrderStatus         int32        `json:"order_status"` // 预约状态：0：没预约，1：预约中
	UpStatus            int32        `json:"up_status"`    // 置顶状态：0：没置顶，1：已置顶
	UpScore             int32        `json:"up_score"`
	LiveOn              int32        `json:"live_on"` // 客户端使用，标识：0=未开播、1=直播中
	Title               string       `json:"title"`
	Cover               string       `json:"cover"`
	CoverId             int64        `json:"cover_id"`
	BgCover             string       `json:"bg_cover"`
	BgCoverId           int64        `json:"bg_cover_id"`
	MemberCnt           int64        `json:"member_cnt"`
	StartTime           int64        `json:"start_time"`
	EndTime             int64        `json:"end_time"`
	Duration            int64        `json:"duration"`
	Income              int64        `json:"income"`
	AnchorIncome        int64        `json:"anchor_income"`
	Heat                int64        `json:"heat"`
	Ver                 string       `json:"ver"`
	Dt                  int32        `json:"dt"`          // 开播dt
	SupportSei          int32        `json:"support_sei"` // 支持sei写入，0：不支持，1：支持
	RecTag              string       `json:"rec_tag"`
	TagUrl              string       `json:"tag_url"`
	AnceTitle           string       `json:"ance_title"`   // 公告标题
	AnceContent         string       `json:"ance_content"` // 公告内容
	ShowID              int64        `json:"show_id"`
	Ticket              int64        `json:"ticket"`
	Joined              bool         `json:"joined"`
	HasPassword         bool         `json:"has_password"`
	Password            string       `json:"password"`
	UpdatePasswordLevel int64        `json:"update_password_level"`
	VipBgCoverLevel     int64        `json:"vip_bg_cover_level"`
	IsSpecialRoom       bool         `json:"is_special_room"`
	SpecialIcon         string       `json:"special_icon"`
	StaticBgCover       string       `json:"static_bg_cover"`
	UpgradeStatus       int32        `json:"upgrade_status"` // 升级消息是否已读 1：未读，0：已读
	LastLevel           int64        `json:"last_level"`     // 升级前等级
	CoverFrame          string       `json:"cover_frame"`    // 封面边框
	UpdatePwdEt         int64        `json:"update_pwd_et"`  // 更换密码过期时间
	CustomBgEt          int64        `json:"custom_bg_et"`   // 更换自定义背景图片过期时间
	CustomBgCover       string       `json:"custom_bg_cover"`
	CustomBgCoverId     int64        `json:"custom_bg_cover_id"`
	CustomBgExpiresIn   int64        `json:"custom_bg_expires_in"`
	EnableCustomBgCover bool         `json:"enable_custom_bg_cover"` // 自定义背景图当前是否可用
	CustomBgEnable      int          `json:"custom_bg_enable"`       // 是否使用自定义背景图片
	ShowBoxIcon         bool         `json:"show_box_icon"`          // 是否显示财宝箱
	RoomHistoryMsg      int          `json:"room_history_msg"`       // 房间历史弹幕开关
	MicCount            [2]int       `json:"mic_count"`              // 房间内麦位上的男女数量，eg:[2,3] 两女三男
	BroadcastId         int64        `json:"broadcast_id"`           // 交友广播ID
	RocketStatus        int64        `json:"rocket_status"`          // 火箭状态；1；1级火箭爆奖中，2；2级火箭爆奖中，3；3级火箭爆奖中
	RoomTemplate        int64        `json:"room_template"`          // 房间模版
	OpenMicStatus       int64        `json:"open_mic_status"`        // 是否关闭上麦
	AlbumStatus         int64        `json:"album_status"`           // 房间相册
	IsKindRoom          int64        `json:"is_kind_room"`           // 房间相册
	GlobalPkStatus      int64        `json:"global_pk_status"`
	IsCollected         bool         `json:"is_collected"`   // 是否已收藏
	WelcomeMsg          string       `json:"welcome_msg"`    // 欢迎消息 type 17
	WelcomeSwitch       int64        `json:"welcome_switch"` // 欢迎开关 1为开，其他为约定传2  type 18
	MicCnt              int64        `json:"mic_cnt"`        // 4.25.0 麦位数量
	RoomTemplateData    TemplateData `json:"room_template_data"`
}

type TemplateData struct {
	TemplateId     int64  `json:"template_id"`       // 模版id
	TemplateName   string `json:"template_name"`     // 模版名称
	TagName        string `json:"tag_name"`          // 标签名称
	TagIcon        string `json:"tag_icon"`          // 标签图标
	GameStatus     string `json:"game_status"`       // 游戏状态
	IsOpenMicApply bool   `json:"is_open_mic_apply"` // 是否开启麦位申请模式
}

// GetRoomWithRoomIDList 获取房间信息
func (d *Client) GetRoomWithRoomIDList(roomIds []int64) (resp GetRoomWithRoomIdsData, err error) {
	uri := "/room/httpapi/get_room_with_room_ids"
	respData := &struct {
		api_client.ApiCode
		Data GetRoomWithRoomIdsData `json:"data"`
	}{}
	param := &struct {
		Mid                  int64   `json:"mid"`
		RoomIds              []int64 `json:"room_ids"`
		NeedFindRoomTemplate bool    `json:"need_find_room_template"`
		NeedMicCount         bool    `json:"need_mic_count"` // 是否需要：麦位数量，仅1+8麦
	}{
		RoomIds: roomIds,
	}

	err = d.apiClient.DoPost(uri, param, respData)
	if err != nil {
		return
	}

	err = respData.Error()
	if err != nil {
		return
	}
	resp = respData.Data
	return
}

package room

import (
	"context"
	"github.com/stretchr/testify/assert"
	"testing"
	"zlutils/yaplay/api_client"
)

func NewRoomTest(ctx context.Context) Client {
	return Client{
		ctx: ctx,
		apiClient: api_client.ApiClient{
			UrlPrefix:  "https://test-api.imfunup.com/test_inner",
			Ctx:        ctx,
			HttpClient: api_client.ShortTimeoutRequestClient,
		},
	}
}

func TestRoom_GetRoomWithRoomIDList(t *testing.T) {
	room := NewRoomTest(context.Background())

	list, err := room.GetRoomWithRoomIDList([]int64{2, 1})
	assert.Nil(t, err)
	assert.Equal(t, 2, len(list.Map))
	for _, st := range list.List {
		t.Logf("%+v", st)
	}
}

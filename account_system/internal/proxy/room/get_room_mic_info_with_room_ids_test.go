package room

import (
	"context"
	"github.com/stretchr/testify/assert"
	"testing"
)

func TestClient_GetRoomMicInfoWithRoomIds(t *testing.T) {
	room := NewRoomTest(context.Background())
	roomIds, err := room.GetRoomMicInfoWithRoomIds([]int64{23189278})
	assert.Nil(t, err)
	for roomId, infos := range roomIds {
		t.Log("roomId:", roomId)
		for _, info := range infos {
			t.Logf("\t mic_info:%+v", info)
		}
	}
}

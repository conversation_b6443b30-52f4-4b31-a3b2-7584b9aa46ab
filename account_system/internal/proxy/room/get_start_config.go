package room

import (
	"github.com/pkg/errors"
	"zlutils/yaplay/api_client"
)

type ConfigData struct {
	IsOpenAppleidAutoLogin bool `json:"is_open_appleid_auto_login"` // 是否开启apple自动账密登录
	IsOpenTmpMidLogin      bool `json:"is_open_tmp_mid_login"`      // 是否放开临时账密登录入口
}

// GetStartConfig 获取苹果临时登录开关数据
func (d *Client) GetStartConfig(mid int64) (configData *ConfigData, err error) {
	res := &struct {
		api_client.ApiCode
		Data *ConfigData `json:"data,omitempty"`
	}{}

	params := map[string]interface{}{
		`mid`: mid,
	}
	if err = d.apiClient.DoPost(`/room/httpapi/config`, params, res); err != nil {
		err = errors.Wrap(err, "room.GetStartConfig")
		return
	}
	if res.Error() != nil {
		err = errors.Wrap(res.Error(), "room.GetStartConfig res.Error")
		return
	}
	configData = res.Data
	return
}

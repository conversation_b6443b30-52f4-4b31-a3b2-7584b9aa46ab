package room

import (
	"context"
	"new-gitlab.xunlei.cn/funup/account_system/internal/config"
	"zlutils/yaplay/api_client"
)

// Client room rpc
type Client struct {
	ctx       context.Context
	apiClient api_client.ApiClient
}

// NewRoomApi msg dispatch
func NewRoomApi(ctx context.Context, urlPrefix string) *Client {
	return &Client{
		ctx: ctx,
		apiClient: api_client.ApiClient{
			UrlPrefix:  urlPrefix,
			Ctx:        ctx,
			HttpClient: api_client.ShortTimeoutRequestClient,
		},
	}
}

// NewRoomApiV2 msg dispatch
func NewRoomApiV2(ctx context.Context) *Client {
	return &Client{
		ctx: ctx,
		apiClient: api_client.ApiClient{
			UrlPrefix:  config.RemoteApiConfig.RoomUrl,
			Ctx:        ctx,
			HttpClient: api_client.ShortTimeoutRequestClient,
		},
	}
}

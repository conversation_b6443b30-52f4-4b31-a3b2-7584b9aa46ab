package room

import "zlutils/yaplay/api_client"

// BatchGetUserStatusData 获取用户在直播间内的状态
type BatchGetUserStatusData struct {
	List   []*UserStatusSt `json:"list"`
	ErrMsg string          `json:"err_msg"`
}

type BatchGetUserStatusDataV2 struct {
	Map map[int64]*UserStatusSt `json:"map"`
}

// UserStatusSt 用户在直播间内的状态
type UserStatusSt struct {
	RoomId int64        `json:"room_id"`
	Mid    int64        `json:"mid"`
	Type   RoomTypeTy   `json:"type"`
	Status UserStatusTy `json:"status"`
	Ut     int64        `json:"ut"`
}

type RoomTypeTy = int32

const (
	UNION_ROOM    RoomTypeTy = 0 // 工会房间
	PERSONAL_ROOM RoomTypeTy = 1 // 个人房间（1v1）
	PAL_ROOM      RoomTypeTy = 2 // 交友房间
)

type UserStatusTy = int32

const (
	OFFLINE_USER UserStatusTy = 0 // 0：不在直播间，离线
	NORMAL_USER  UserStatusTy = 1 // 1：普通观众
	ON_MIC_USER  UserStatusTy = 2 // 2：麦上
	ANCHOR_USER  UserStatusTy = 3 // 3：主持位
)

// BatchGetUserStatus 获取用户在房状态
// notSteal 是否应该判断隐身
func (d *Client) BatchGetUserStatus(mids []int64, notSteal bool) (userStatus map[int64]*UserStatusSt, err error) {
	userStatus = make(map[int64]*UserStatusSt, 0)

	uri := "/room/httpapi/batch_get_user_status"
	respData := &struct {
		api_client.ApiCode
		Data BatchGetUserStatusData `json:"data"`
	}{}
	param := &struct {
		Mids         []int64 `json:"mids"`
		NotNeedSteal bool    `json:"not_need_steal"`
	}{
		Mids: mids,
	}

	err = d.apiClient.DoPost(uri, param, respData)
	if err != nil {
		return
	}

	err = respData.Error()
	if err != nil {
		return
	}
	for _, v := range respData.Data.List {
		userStatus[v.Mid] = v
	}
	return
}

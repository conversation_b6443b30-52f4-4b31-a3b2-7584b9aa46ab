package trade

import (
	"github.com/pkg/errors"
	"zlutils/yaplay/api_client"
)

// ClearUserUsd 美金清零
func (d *Client) ClearUserUsd(mid int64) (err error) {
	uri := "/trade_biz_cn/rpc/v1/trade/clear_user_usd"

	respData := &struct {
		api_client.ApiCode
	}{}

	err = d.apiClient.DoPost(uri, map[string]interface{}{
		`mid`: mid,
	}, respData)
	if err != nil {
		err = errors.Wrap(err, "do post err")
		return
	}

	err = respData.Error()
	if err != nil {
		err = errors.Wrap(err, "resp data has err ")
		return
	}
	return
}

package trade

import (
	"github.com/pkg/errors"
	"zlutils/yaplay/api_client"
)

// UpdateUserMoneyParams 更新玩家的金钱
type UpdateUserMoneyParams struct {
	Mid     int64  `json:"mid"`
	Cnt     int64  `json:"cnt"`
	Type    string `json:"type"`
	ExtId   string `json:"ext_id"`
	From    string `json:"from"`
	ExtInfo string `json:"ext_info,omitempty"`
}

// UpdateUserMoneyResp resp
type UpdateUserMoneyResp struct {
	api_client.ApiCode
	Data struct {
		Status int64 `json:"status"`
	} `json:"data"`
}

// UpdateUserMoney 更新玩家的金钱.
// status 状态. 1 成功,2 玩家货币不足 3 重复提交
func (d *Client) UpdateUserMoney(params *UpdateUserMoneyParams) (success bool, status int64, err error) {
	uri := "/trade/httpapi/update_user_money"

	respData := &UpdateUserMoneyResp{}

	err = d.apiClient.DoPost(uri, params, respData)
	if err != nil {
		err = errors.Wrap(err, "do post err")
		return
	}

	err = respData.Error()
	if err != nil {
		err = errors.Wrap(err, "resp data has err ")
		return
	}
	status = respData.Data.Status
	success = status == 1
	return
}

package trade

import (
	"github.com/pkg/errors"
	"zlutils/yaplay/api_client"
)

// GetUserUsd 获取美金
func (d *Client) GetUserUsd(mids []int64) (userUsdMap map[int64]int64, err error) {
	uri := "/trade_biz_cn/rpc/v1/trade/get_user_usd"

	respData := &struct {
		api_client.ApiCode
		Data struct {
			UserUsdMap map[int64]int64 `json:"user_usd_map"`
		} `json:"data"`
	}{}

	err = d.apiClient.DoPost(uri, map[string]interface{}{
		`mids`: mids,
	}, respData)
	if err != nil {
		err = errors.Wrap(err, "do post err")
		return
	}

	err = respData.Error()
	if err != nil {
		err = errors.Wrap(err, "resp data has err ")
		return
	}
	userUsdMap = respData.Data.UserUsdMap
	return
}

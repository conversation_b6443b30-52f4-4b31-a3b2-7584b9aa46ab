package trade

import (
	"context"
	"zlutils/yaplay/api_client"
)

// Client Trade rpc
type Client struct {
	ctx       context.Context
	apiClient api_client.ApiClient
}

// NewTradeApiClient trade api
func NewTradeApiClient(ctx context.Context, urlPrefix string) *Client {
	return &Client{
		ctx: ctx,
		apiClient: api_client.ApiClient{
			UrlPrefix:  urlPrefix,
			Ctx:        ctx,
			HttpClient: api_client.LongTimeoutRequestClient,
		},
	}
}

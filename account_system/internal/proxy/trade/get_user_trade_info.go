package trade

import (
	"github.com/pkg/errors"
	"zlutils/yaplay/api_client"
)

// GetUserTradeInfo 获取玩家的金钱.
func (d *Client) GetUserTradeInfo(mid int64) (coins, tickets int64, err error) {
	uri := "/trade/httpapi/get_user_trade_info"

	respData := &struct {
		api_client.ApiCode
		Data struct {
			Coins   int64 `json:"coins"`
			Tickets int64 `json:"tickets"`
		} `json:"data"`
	}{}

	err = d.apiClient.DoPost(uri, map[string]interface{}{
		`mid`: mid,
	}, respData)
	if err != nil {
		err = errors.Wrap(err, "do post err")
		return
	}

	err = respData.Error()
	if err != nil {
		err = errors.Wrap(err, "resp data has err ")
		return
	}
	coins = respData.Data.Coins
	tickets = respData.Data.Tickets
	return
}

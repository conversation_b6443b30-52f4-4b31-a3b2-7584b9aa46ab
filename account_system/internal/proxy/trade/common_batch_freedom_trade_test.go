package trade

import (
	"context"
	"github.com/stretchr/testify/assert"
	"testing"
)

func TestClient_CommonBatchFreedomTrade(t *testing.T) {
	client := NewTradeApiClient(context.Background(), "https://test-api.imfunup.com/test_inner")

	success, newCoins, status, err := client.CommonBatchFreedomTrade(&CommonBatchFreedomTradeParam{
		FromMid:      5,
		FromCount:    -1,
		FromRoomID:   2,
		FromCurrency: Coins,
		From:         HeartbeatGameDecrCoins,
		ToMidsInfo: []*CommonTradeFreedomToMidInfoSt{
			{
				Mid:      2000027,
				RoomID:   2,
				Currency: Tickets,
				From:     HeartbeatGameIncrTickets,
				Count:    7,
			},
			{
				Mid:      2000027,
				RoomID:   2,
				Currency: Tickets,
				From:     HeartbeatGameIncrTickets,
				Count:    14,
			},
		},
	})
	assert.Nil(t, err)
	assert.True(t, success)
	t.Log("coins", newCoins, " status:", status)
}

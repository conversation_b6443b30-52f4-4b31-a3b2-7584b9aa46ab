package trade

import (
	"github.com/pkg/errors"
	"zlutils/yaplay/api_client"
)

type From string

var (
	// HeartbeatGameDecrCoins 从游戏中扣除金币
	HeartbeatGameDecrCoins From = "heartbeat_game_decr_coins"
	// HeartbeatGameIncrTickets 从游戏中增加钻石
	HeartbeatGameIncrTickets From = "heartbeat_game_incr_tickets"
)

type Currency string

var (
	// Coins 金币
	Coins Currency = "coins"
	// Tickets 钻石
	Tickets Currency = "tickets"
)

// CommonBatchFreedomTradeParam 自由交易
type CommonBatchFreedomTradeParam struct {
	FromMid      int64                            `json:"from_mid"`      // 交易发起者
	FromCount    int64                            `json:"from_count"`    // 交易发起者交易数量,如果是扣除的话要是负数
	FromRoomID   int64                            `json:"from_room_id"`  // 交易发起者房间号
	FromCurrency Currency                         `json:"from_currency"` // 交易发起者交易币种
	From         From                             `json:"from"`          // 交易发起者交易来源
	ExtID        string                           `json:"ext_id"`
	ExtInfo      string                           `json:"ext_info"`
	ToMidsInfo   []*CommonTradeFreedomToMidInfoSt `json:"to_mids_info"` // 交易接收者信息
}

// CommonTradeFreedomToMidInfoSt 自由交易接收者信息
type CommonTradeFreedomToMidInfoSt struct {
	Mid      int64    `json:"mid"`      // 交易接收者
	RoomID   int64    `json:"room_id"`  // 交易接收者房间号.
	Currency Currency `json:"currency"` // 交易接收者交易币种
	From     From     `json:"from"`     // 交易接收者交易来源
	Count    int      `json:"count"`    // 交易接收者交易数量
}

// CommonBatchFreedomTrade 更新玩家的金钱.
// status 状态. 1 成功,2 玩家货币不足 3 重复提交
func (d *Client) CommonBatchFreedomTrade(params *CommonBatchFreedomTradeParam) (success bool, newCoins, status int64, err error) {
	uri := "/trade/httpapi/common_batch_freedom_trade"

	respData := &struct {
		api_client.ApiCode
		Data struct {
			Status int64 `json:"status"`
			Coins  int64 `json:"coins,omitempty"`
		} `json:"data"`
	}{}

	err = d.apiClient.DoPost(uri, params, respData)
	if err != nil {
		err = errors.Wrap(err, "do post err")
		return
	}

	err = respData.Error()
	if err != nil {
		err = errors.Wrap(err, "resp data has err ")
		return
	}
	status = respData.Data.Status
	newCoins = respData.Data.Coins
	success = status == 1
	return
}

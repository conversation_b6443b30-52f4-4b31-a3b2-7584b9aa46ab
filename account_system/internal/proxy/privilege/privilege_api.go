package privilege

import (
	"context"
	"github.com/sirupsen/logrus"
	"new-gitlab.xunlei.cn/funup/account_system/internal/proxy/account"
	"time"
	"zlutils/yaplay/api_client"
)

// ApiClient api client
type ApiClient struct {
	api_client.ApiClient
}

// NewApiClient 新建一个api client
func NewApiClient(ctx context.Context, url string) *ApiClient {
	apiClient := &ApiClient{
		ApiClient: api_client.ApiClient{
			UrlPrefix:  url,
			Ctx:        ctx,
			HttpClient: api_client.ShortTimeoutRequestClient,
		},
	}

	return apiClient
}

type GetAllPrivilegeListParam struct {
	account.BaseParm
	Mid       int64 `json:"mid"`
	RetExpire bool  `json:"ret_expire"` // 是否返回已过期的
}

type GetAllPrivilegeListResp struct {
	api_client.ApiCode
	Data GetAllPrivilegeListData `json:"data"`
}

// 获取用户当前未过期的所有特权
type GetAllPrivilegeListData struct {
	ErrMsg        string         `json:"err_msg"`
	PrivilegeList []*PrivilegeSt `json:"privilege_list"`
	Dot           bool           `json:"dot"`           // 是否显示红点
	GiftsDot      bool           `json:"gifts_dot"`     // 经典Tab,是否显示红点
	LuckyBagDot   bool           `json:"lucky_bag_dot"` // 福袋Tab,是否显示红点
}
type PrivilegeIdTy = int64

type PrivilegeSt struct {
	PrivilegeId PrivilegeIdTy `json:"privilege_id"` // 特权id

	Name  string   `json:"name,omitempty"`  // 特权整体的名称
	Medal *MedalSt `json:"medal,omitempty"` // 勋章
}

type ExpireStatusTy = int32

const (
	// 过期状态，0：未过期，1：已过期
	NOT_EXPIRE     ExpireStatusTy = 0
	ALREADY_EXPIRE ExpireStatusTy = 1
)

type MedalSt struct {
	Id             int64          `json:"id"`                  // 勋章id
	Name           string         `json:"name"`                // 勋章名
	NameZh         string         `json:"name_zh"`             // 勋章名
	Icon           string         `json:"icon"`                // 勋章
	DmkIcon        string         `json:"dmk_icon"`            // 弹幕区显示的勋章
	IconV2         string         `json:"icon_v2"`             // 4.27.0新的勋章url @李游/@林鹏 之后使用这个字段
	ExpireStatus   ExpireStatusTy `json:"expire_status"`       // 过期状态
	ExpireDuration int64          `json:"expire_duration"`     // 过期秒数
	Condition      string         `json:"condition"`           // 获取条件
	Et             int64          `json:"et"`                  // 过期时间，毫秒时间戳
	HwRatio        float64        `json:"hw_ratio"`            // 高宽比，定义：高/宽 默认值：14/40
	Msg            string         `json:"msg,omitempty"`       // 图片上的文字
	MsgColor       string         `json:"msg_color,omitempty"` // 图片上的文字的颜色
	MedalType      int64          `json:"medal_type"`          // 4.27.0新增：1：默认类型；2：铭牌勋章
	MedalLevel     int64          `json:"medal_level"`         // 未来新增加的排序等级排
}

// GetAllPrivilegeList 获取用户未过期的特权
func (a *ApiClient) GetAllPrivilegeList(mid int64) (resp *GetAllPrivilegeListResp, err error) {

	logger := logrus.WithContext(a.Ctx).WithFields(logrus.Fields{
		"func": "ApiClient.GetAllPrivilegeList",
		"req":  time.Now().UnixNano(),
	})
	logger.Info()

	resp = &GetAllPrivilegeListResp{}
	req := &GetAllPrivilegeListParam{
		BaseParm:  account.BaseParm{},
		Mid:       mid,
		RetExpire: false,
	}

	uri := "/privilege/httpapi/get_all_privilege"
	err = a.DoPost(uri, req, resp)
	if err != nil {
		logger.Errorf("do request failed. %s", err)
		return
	}

	err = resp.Error()
	if err != nil {
		logger.Errorf("api return error. %s", err)
		return
	}
	return
}

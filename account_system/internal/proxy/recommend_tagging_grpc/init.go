package recommend_tagging_grpc

import (
	"context"
	"fmt"
	"github.com/grpc-ecosystem/grpc-opentracing/go/otgrpc"
	_ "github.com/mbobakov/grpc-consul-resolver"
	"github.com/sirupsen/logrus"
	"google.golang.org/grpc"
	"google.golang.org/grpc/credentials/insecure"
	"google.golang.org/grpc/metadata"
	"new-gitlab.xunlei.cn/funup/yaplay_proto/recommend_tagging"
	"os"
	"time"
	"zlutils/yaplay/jaeger_tracer"
	"zlutils/yaplay/log"
)

var (
	grpcUrl string

	grpcConn *grpc.ClientConn
	client   recommend_tagging.AppServiceClient
)

func Init() {
	env := os.Getenv("stage")
	if env == `dev` {
		grpcUrl = fmt.Sprintf("consul://consul-prod.imfunup.com:8500/hiya_%s-recommend_tagging", env)
	} else if env == `test` {
		grpcUrl = fmt.Sprintf("consul://consul-prod.imfunup.com:8500/hiya_%s-recommend_tagging", env)
	} else {
		grpcUrl = fmt.Sprintf("consul://consul-prod.imfunup.com:8500/hiya_%s-recommend_tagging", env)
	}
	initGrpcConn(grpcUrl)
	grpcConn = GetGrpcConn()
	client = recommend_tagging.NewAppServiceClient(grpcConn)
}

func initGrpcConn(grpcUrl string) {
	var err error
	var ctx, cancelFunc = context.WithTimeout(context.Background(), 5*time.Second)
	defer cancelFunc()

	tracer := jaeger_tracer.GetGlobalTracer()
	grpcConn, err = grpc.DialContext(ctx, grpcUrl,
		grpc.WithTransportCredentials(insecure.NewCredentials()),
		grpc.WithBlock(), grpc.WithDefaultServiceConfig(`{"loadBalancingPolicy": "round_robin"}`),
		grpc.WithStreamInterceptor(
			otgrpc.OpenTracingStreamClientInterceptor(tracer),
		),
		grpc.WithUnaryInterceptor(
			otgrpc.OpenTracingClientInterceptor(tracer),
		),
	)

	if err != nil {
		logrus.WithError(err).WithField(`urlPrefix`, grpcUrl).Panic("did not connect.")
	}
}

func GetGrpcConn() *grpc.ClientConn {
	return grpcConn
}

func GetGrpcClient() recommend_tagging.AppServiceClient {
	return client
}

func Ctx(ctx context.Context) context.Context {
	return metadata.NewOutgoingContext(ctx, metadata.Pairs(log.TraceIdKey, log.GetTraceId(ctx)))
}

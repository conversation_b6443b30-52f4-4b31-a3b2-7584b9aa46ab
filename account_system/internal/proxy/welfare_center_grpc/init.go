package welfare_center_grpc

import (
	"fmt"
	"google.golang.org/grpc"
	"new-gitlab.xunlei.cn/funup/yaplay_proto/welfare_center"
	"os"
	"zlutils/yaplay/grpc_client_util"
)

var (
	GrpcUrl string

	grpcConn              *grpc.ClientConn
	newPropServiceClient  welfare_center.PropServiceClient
	activityServiceClient welfare_center.ActivityServiceClient
	taskServiceClient     welfare_center.TaskServiceClient
)

func Init() {
	env := os.Getenv("stage")
	if env == `dev` || env == "" {
		GrpcUrl = fmt.Sprintf("consul://consul-prod.imfunup.com:8500/hiya_%s-welfare_center", env)
	} else if env == `test` {
		GrpcUrl = fmt.Sprintf("consul://consul-prod.imfunup.com:8500/hiya_%s-welfare_center", env)
	} else {
		GrpcUrl = fmt.Sprintf("consul://consul-prod.imfunup.com:8500/hiya_%s-welfare_center", env)
	}
	var err error
	grpcConn, err = grpc_client_util.InitGrpcConn(GrpcUrl)

	if err != nil {
		//logrus.WithError(err).WithFields(logrus.Fields{
		//	`grpcUrl`: GrpcUrl,
		//}).Panic("init grpc conn failed.")
		return
	}
	grpcConn = getGrpcConn()
	newPropServiceClient = welfare_center.NewPropServiceClient(grpcConn)
	activityServiceClient = welfare_center.NewActivityServiceClient(grpcConn)
	taskServiceClient = welfare_center.NewTaskServiceClient(grpcConn)
}

func getGrpcConn() *grpc.ClientConn {
	return grpcConn
}

func GetPropServiceClient() welfare_center.PropServiceClient {
	return newPropServiceClient
}

func GetActivityServiceClient() welfare_center.ActivityServiceClient {
	return activityServiceClient
}

func GetTaskServiceClient() welfare_center.TaskServiceClient {
	return taskServiceClient
}

package hiya_scheme

import (
	"strconv"
	"strings"
)

// OpenFeedDetail 跳转到动态详情页
type OpenFeedDetail struct {
	from   string // 来源
	feedId int    // 动态ID
	cid    int    // 评论ID
}

// NewOpenFeedDetailBuilder builder
func NewOpenFeedDetailBuilder() *OpenFeedDetail {
	return &OpenFeedDetail{}
}

// MustFrom 来源
func (o *OpenFeedDetail) MustFrom(f string) *OpenFeedDetail {
	o.from = f
	return o
}

// MustFeedId feed id
func (o *OpenFeedDetail) MustFeedId(feedID int) *OpenFeedDetail {
	o.feedId = feedID
	return o
}

// Cid cid 评论id
func (o *OpenFeedDetail) Cid(cid int) *OpenFeedDetail {
	o.cid = cid
	return o
}

func (o *OpenFeedDetail) String() string {
	var s strings.Builder

	// hiya://openPostDetail?from=web&fid=1&cid=1
	s.WriteString(`hiya://openPostDetail?from=`)
	s.WriteString(o.from)
	s.WriteString(`&fid=`)
	s.WriteString(strconv.Itoa(o.feedId))
	if o.cid != 0 {
		s.WriteString(`&cid=`)
		s.WriteString(strconv.Itoa(o.cid))
	}
	return s.String()
}

package hiya_scheme

import (
	"testing"

	"github.com/stretchr/testify/assert"
)

func TestOpenFeedDetail_String(t *testing.T) {
	var scheme string

	scheme = NewOpenFeedDetailBuilder().MustFeedId(1).MustFrom(`web`).String()
	assert.EqualValues(t, scheme, `hiya://openPostDetail?from=web&fid=1`)

	scheme = NewOpenFeedDetailBuilder().MustFeedId(1).MustFrom(`web`).Cid(1).String()
	assert.EqualValues(t, scheme, `hiya://openPostDetail?from=web&fid=1&cid=1`)
}

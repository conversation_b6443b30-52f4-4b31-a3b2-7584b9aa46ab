package hiya_scheme

import (
	"fmt"
	"strconv"
	"strings"
)

// OpenRoom 打开直播间
type OpenRoom struct {
	from       string // 来源,可以随意填，但是最后会算到进房埋点中
	upStatus   int    // 0 不自动上麦，1自动上麦
	fromPostID int    // 来源帖子ID
	tagID      int    // 来源话题ID
	roomID     int    // 跳转的目标房间id
	scheme     string // 进入语音房后，默认打开的schemeUrl，支持hiya://协议  4.5.1版本添加
}

// NewOpenRoomBuilder builder
func NewOpenRoomBuilder() *OpenRoom {
	return &OpenRoom{}
}

// From 来源,可以随意填，但是最后会算到进房埋点中
func (o *OpenRoom) From(f string) *OpenRoom {
	o.from = f
	return o
}

// AutoUpMic 自动上麦
func (o *OpenRoom) AutoUpMic() *OpenRoom {
	o.upStatus = 1
	return o
}

// FromPostID 来源帖子ID
func (o *OpenRoom) FromPostID(postID int) *OpenRoom {
	o.fromPostID = postID
	return o
}

// TagID 来源帖子ID
func (o *OpenRoom) TagID(tagID int) *OpenRoom {
	o.tagID = tagID
	return o
}

// MustRoomID 设置需要跳转的房间ID
func (o *OpenRoom) MustRoomID(roomID int) *OpenRoom {
	o.roomID = roomID
	return o
}

// String string
func (o *OpenRoom) String() string {
	var scheme strings.Builder
	scheme.WriteString(`hiya://openRoom?room_id=`)
	scheme.WriteString(strconv.Itoa(o.roomID))
	if o.from != "" {
		scheme.WriteString(fmt.Sprintf(`&from=%s`, o.from))
	}
	if o.upStatus != 0 {
		scheme.WriteString(fmt.Sprintf(`&up_status=%d`, o.upStatus))
	}
	if o.fromPostID != 0 {
		scheme.WriteString(fmt.Sprintf(`&from_post_id=%d`, o.fromPostID))
	}
	if o.tagID != 0 {
		scheme.WriteString(fmt.Sprintf(`&tag_id=%d`, o.tagID))
	}
	return scheme.String()
}

package hiya_scheme

import "fmt"

// NewOpenVisitorListScheme 打开访客页面
func NewOpenVisitorListScheme(from OpenVisitorListFrom) string {
	return fmt.Sprintf("hiya://openVisitorList??from=%d", from)
}

type OpenVisitorListFrom int

const (
	OpenVisitorListFromPush OpenVisitorListFrom = iota
	OpenVisitorListFromMy
	OpenVisitorListFromRoomProfileCard
	OpenVisitorListFromMyProfile
	OpenVisitorListFromOtherProfile
)

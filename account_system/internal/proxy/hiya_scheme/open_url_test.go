package hiya_scheme

import (
	"github.com/stretchr/testify/assert"
	"testing"
)

func TestNewOpenUrlBuilder(t *testing.T) {
	build := NewOpenUrlBuilder().MustUrl(`https://www.baidu.com#a=1&b=3`).
		FullScreen(0).
		OtherInfo(map[string]interface{}{`tab`: 1}).
		Build()
	assert.Equal(t, build,
		`hiya://openUrl?url=https%3A%2F%2Fwww.baidu.com%23a%3D1%26b%3D3&other_info=%7b%22tab%22%3a1%7d&need_user_info=1&external=0&fullscreen=0&close_current=0&bgcolor=&h_ratio=0.000000`)
}

package hiya_scheme

import "fmt"

// OpenNotifications 打开通知页面
type OpenNotifications struct {
	tabId int
}

// NewOpenNotificationsBuilder 打开通知页面
func NewOpenNotificationsBuilder() *OpenNotifications {
	return &OpenNotifications{}
}

// Interactive 互动页面
func (o *OpenNotifications) Interactive() *OpenNotifications {
	o.tabId = 0
	return o
}

// TheNewFocusOn 新增通知
func (o *OpenNotifications) TheNewFocusOn() *OpenNotifications {
	o.tabId = 1
	return o
}

// String str
func (o *OpenNotifications) String() string {
	return fmt.Sprintf(`hiya://openNotifications?tabid=%d`, o.tabId)
}

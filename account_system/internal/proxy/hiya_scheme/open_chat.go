package hiya_scheme

import (
	"strconv"
	"strings"
)

// OpenChat 打开聊天页面
type OpenChat struct {
	// hiya://openChat?uid=10013&operationtType=0
	mid           int
	operationType int
}

func NewOpenChatBuilder() *OpenChat {
	return &OpenChat{}
}

// MustMid 需要打开的聊天界面对应的人
func (o *OpenChat) MustMid(mid int) *OpenChat {
	o.mid = mid
	return o
}

// Keyboard 还需要拉起键盘
func (o *OpenChat) Keyboard() *OpenChat {
	o.operationType = 1
	return o
}

// Gift 还需要拉起礼物面板
func (o *OpenChat) Gift() *OpenChat {
	o.operationType = 2
	return o
}

// Backpack 还需要拉起背包
func (o *OpenChat) Backpack() *OpenChat {
	o.operationType = 3
	return o
}

func (o *OpenChat) String() string {
	var scheme strings.Builder
	scheme.WriteString(`hiya://openChat?uid=`)
	scheme.WriteString(strconv.Itoa(o.mid))
	if o.operationType != 0 {
		scheme.WriteString(`&operationtType=`)
		scheme.WriteString(strconv.Itoa(o.operationType))
	}
	return scheme.String()
}

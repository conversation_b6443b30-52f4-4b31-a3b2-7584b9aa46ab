package hiya_scheme

import "fmt"

// OpenHome 打开主页
type OpenHome struct {
	tabId int // 默认0； 【0 约玩 1 娱乐】
	from  string
}

// NewOpenHomeYueWanScheme 打开约玩
func NewOpenHomeYueWanScheme(from string) string {
	return fmt.Sprintf(`hiya://openHome?tab=%d&from=%s`, 0, from)
}

// NewOpenHomeYuLeScheme 打开娱乐
func NewOpenHomeYuLeScheme(from string) string {
	return fmt.Sprintf(`hiya://openHome?tab=%d&from=%s`, 1, from)
}

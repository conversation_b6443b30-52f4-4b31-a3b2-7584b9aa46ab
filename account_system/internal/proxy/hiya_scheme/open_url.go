package hiya_scheme

import (
	"encoding/json"
	"fmt"
	"net/url"
)

// OpenUrl 打开web页面., Android 4.5.0支持
type OpenUrl struct {
	// hiya://openUrl?url=xxx
	url       string // 打开H5页面的地址, 参数拼接时需要进行URL编码
	otherInfo string // 透传给H5的参数

	needUserInfo    int // 是否需要用户信息, 0: 不需要, 1: 需要
	withoutUserInfo bool

	external     int     // 是否使用外部浏览器打开, 0: 不使用, 1: 使用
	fullscreen   int     // 是否全屏显示, (external=0时生效  0:带头部NAV样式  1:全屏样式 2:半屏浮窗样式 3:直播间全屏覆盖样式；默认值 0)
	closeCurrent int     // 是否关闭当前页面,(external=0时生效  0:不关闭当前页面  1:跳转到新页面后关闭当前页面 默认值 0 )
	bgColor      string  // WebView背景色，8位的颜色字符串
	hRatio       float64 // 半屏WebView弹窗的高度
}

// NewOpenUrlBuilder 打开web页面
func NewOpenUrlBuilder() *OpenUrl {
	return &OpenUrl{}
}

func (o *OpenUrl) MustUrl(urls string) *OpenUrl {
	parse := url.QueryEscape(urls)
	o.url = parse
	return o
}

func (o *OpenUrl) OtherInfo(otherInfo interface{}) *OpenUrl {
	bytes, err := json.Marshal(otherInfo)
	if err != nil {
		return o
	}

	parse := url.QueryEscape(string(bytes))
	o.otherInfo = parse
	return o
}

func (o *OpenUrl) WithoutUserInfo() *OpenUrl {
	o.needUserInfo = 0
	o.withoutUserInfo = true
	return o
}

// External 设置为外部浏览器打开.
func (o *OpenUrl) External() *OpenUrl {
	o.external = 1
	return o
}

// FullScreen 是否全屏显示, (external=0时生效  0:带头部NAV样式  1:全屏样式 2:半屏浮窗样式 3:直播间全屏覆盖样式；默认值 0)
func (o *OpenUrl) FullScreen(full int) *OpenUrl {
	o.fullscreen = full
	return o
}

// CloseCurrent (external=0时生效  0:不关闭当前页面  1:跳转到新页面后关闭当前页面 默认值 0 )
func (o *OpenUrl) CloseCurrent() *OpenUrl {
	o.closeCurrent = 1
	return o
}

// BgColor WebView背景色，8位的颜色字符串
func (o *OpenUrl) BgColor(bgColor string) *OpenUrl {
	o.bgColor = bgColor
	return o
}

// HRatio 半屏WebView弹窗的高度
func (o *OpenUrl) HRatio(hRatio float64) *OpenUrl {
	o.hRatio = hRatio
	return o
}

// Build 构建
func (o *OpenUrl) Build() string {
	if !o.withoutUserInfo {
		o.needUserInfo = 1
	}
	return fmt.Sprintf("hiya://openUrl?url=%s&other_info=%v&need_user_info=%d&external=%d&fullscreen=%d&close_current=%d&bgcolor=%s&h_ratio=%f",
		o.url, o.otherInfo, o.needUserInfo, o.external, o.fullscreen, o.closeCurrent, o.bgColor, o.hRatio)
}

package hiya_scheme

import (
	"testing"

	"github.com/stretchr/testify/assert"
)

func TestOpenRoom_String(t *testing.T) {
	scheme := NewOpenRoomBuilder().MustRoomID(8).From(`f`).AutoUpMic().FromPostID(1).TagID(2).String()

	assert.EqualValues(t, scheme, `hiya://openRoom?room_id=8&from=f&up_status=1&from_post_id=1&tag_id=2`)

	scheme = NewOpenRoomBuilder().MustRoomID(8).String()

	assert.EqualValues(t, scheme, `hiya://openRoom?room_id=8`)

}

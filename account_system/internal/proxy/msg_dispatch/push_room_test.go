package msg_dispatch

import (
	"context"
	"new-gitlab.xunlei.cn/funup/yaplay_proto/msgdispatcher"
	"testing"
)

func TestMsgDispatch_PushRoom(t *testing.T) {
	dispatch := NewMsgDispatch(context.Background(), "https://test-api.imfunup.com/test_inner")

	_ = dispatch.PushRoom(PushRoomParam{
		Type:      msgdispatcher.PushType_MsgTypeTextPushMsg2,
		Mid:       5,
		ToRoomIds: []int64{23189278},
		SrcRoomID: 23189278,
		PushData: msgdispatcher.TextPushMsgData{
			Msg:  "系统正在维护中，暂不支持发弹幕",
			Type: msgdispatcher.TextPushMsgData_Plain,
		},
	})
}

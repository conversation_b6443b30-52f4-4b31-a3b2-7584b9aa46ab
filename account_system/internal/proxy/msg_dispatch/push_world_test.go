package msg_dispatch

import (
	"context"
	"fmt"
	"github.com/stretchr/testify/assert"
	"new-gitlab.xunlei.cn/funup/account_system/internal/proxy/hiya_scheme"
	"new-gitlab.xunlei.cn/funup/yaplay_proto/msgdispatcher"
	"testing"
)

func TestMsgDispatch_PushWorld(t *testing.T) {
	dispatch := NewMsgDispatch(context.Background(), "https://test-api.imfunup.com/test_inner")

	//err := dispatch.PushWorld(PushWorldParam{
	//	Type:      msgdispatcher.PushType_MsgTypePublicScreenPush,
	//	Mid:       5,
	//	SrcRoomID: ********,
	//	PushData: msgdispatcher.TextPushMsgData{
	//		Msg: fmt.Sprintf("Hello,Teemo"),
	//	},
	//	IsSecret: true,
	//})

	err := dispatch.PushWorld(PushWorldParam{
		Type:      msgdispatcher.PushType_MsgTypePublicScreenPush,
		Mid:       5,
		SrcRoomID: ********,
		PushData: msgdispatcher.PublicScreenPushData{
			Msg:          fmt.Sprintf("Hello,${Teemo}"),
			Highlights:   []string{"#FFE4E1"},
			JumpUrl:      hiya_scheme.NewOpenRoomBuilder().MustRoomID(********).String(),
			ConfirmTitle: "温馨提示",
			ConfirmText:  "是否跳转到该房间",
			SubmitText:   "老司机带带我",
			CancelText:   "取消",
			BgColor:      "#FFB6C1",
			BorderColor:  "#4169E1",
			MarkText:     "心动点单",
			MarkBgColor:  "#000000",
		},
		IsSecret: true,
	})
	assert.Nil(t, err)
}

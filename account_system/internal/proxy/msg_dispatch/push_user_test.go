package msg_dispatch

import (
	"context"
	"github.com/stretchr/testify/assert"
	"new-gitlab.xunlei.cn/funup/yaplay_proto/msgdispatcher"
	"testing"
)

func TestMsgDispatch_PushUser(t *testing.T) {
	dispatch := NewMsgDispatch(context.Background(), "https://test-api.imfunup.com/test_inner")

	err := dispatch.PushUser(PushUserParam{
		Type:   msgdispatcher.PushType_MsgTypeSystemPush,
		Mid:    5,
		RoomId: 23189278,
		PushData: msgdispatcher.TextPushMsgData{
			Msg:  "不能发布违禁词,请规范发布内容",
			Type: msgdispatcher.TextPushMsgData_Plain,
		},
	})
	assert.Nil(t, err)
}

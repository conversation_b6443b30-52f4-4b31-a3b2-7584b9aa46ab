package msg_dispatch

import (
	"github.com/pkg/errors"
	"new-gitlab.xunlei.cn/funup/yaplay_proto/msgdispatcher"
	"zlutils/yaplay/api_client"
)

// PushWorldParam push world
type PushWorldParam struct {
	Type           msgdispatcher.PushType `json:"type"` //push msg type defined in msgdispatcher
	Mid            int64                  `json:"mid"`
	ExcludeRoomIds []int64                `json:"exclude_room_ids"`
	SrcRoomID      int64                  `json:"src_room_id"`
	PushData       interface{}            `json:"push_data"`
	IsSecret       bool                   `json:"is_secret"` //为true则需要判断如果源房间SrcRoomID锁房（有密码），则不推送到全网房间，只推送到本房间
}

// PushWorldResp resp
type PushWorldResp struct {
	api_client.ApiCode
}

// PushWorld push world
func (d *MsgDispatch) PushWorld(param PushWorldParam) error {

	uri := "/msgdispatcher/httpapi/push_world"
	respData := &PushWorldResp{}

	err := d.apiClient.DoPost(uri, param, respData)
	if err != nil {
		err = errors.Wrap(err, "push world failed")
		return err
	}

	err = respData.Error()
	if err != nil {
		err = errors.Wrap(err, "push world resp failed")
		return err
	}

	return nil
}

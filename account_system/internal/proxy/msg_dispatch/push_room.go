package msg_dispatch

import (
	"context"
	"github.com/pkg/errors"
	"new-gitlab.xunlei.cn/funup/yaplay_proto/msgdispatcher"
	"zlutils/yaplay/api_client"
)

// MsgDispatch msg dispatch
type MsgDispatch struct {
	ctx       context.Context
	apiClient api_client.ApiClient
}

// NewMsgDispatch msg dispatch
func NewMsgDispatch(ctx context.Context, urlPrefix string) MsgDispatch {
	return MsgDispatch{
		ctx: ctx,
		apiClient: api_client.ApiClient{
			UrlPrefix:  urlPrefix,
			Ctx:        ctx,
			HttpClient: api_client.ShortTimeoutRequestClient,
		},
	}
}

// PushRoomParam push room
type PushRoomParam struct {
	Type        msgdispatcher.PushType `json:"type"`        // push msg type defined in msgdispatcher
	Mid         int64                  `json:"mid"`         // 发送的人的mid，我猜的
	ToRoomIds   []int64                `json:"to_room_ids"` // 要发送的room_id
	SrcRoomID   int64                  `json:"src_room_id"` // 源room_id
	ExcludeMids []int64                `json:"exclude_mids"`
	IncludeMids []int64                `json:"include_mids"`
	PushData    interface{}            `json:"push_data"`
}

// PushRoomParamResp resp
type PushRoomParamResp struct {
	api_client.ApiCode
}

// PushRoom push room
func (d *MsgDispatch) PushRoom(param PushRoomParam) error {

	uri := "/msgdispatcher/httpapi/push_room"
	respData := &PushRoomParamResp{}

	err := d.apiClient.DoPost(uri, param, respData)
	if err != nil {
		err = errors.Wrap(err, "push room failed")
		return err
	}

	err = respData.Error()
	if err != nil {
		err = errors.Wrap(err, "push room resp failed")
		return err
	}

	return nil
}

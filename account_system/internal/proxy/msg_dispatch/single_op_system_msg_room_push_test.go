package msg_dispatch

import (
	"context"
	"github.com/stretchr/testify/assert"
	"testing"
)

func TestMsgDispatch_SingleOpSystemMsgRoomPush(t *testing.T) {
	dispatch := NewMsgDispatch(context.Background(), "https://test-api.imfunup.com/test_inner")

	err := dispatch.SingleOpSystemMsgRoomPush(SingleOpSystemMsgRoomPushParam{
		RoomId: 23189278,
		Msg:    "Hello World",
		Mid:    5,
	})
	assert.Nil(t, err)
}

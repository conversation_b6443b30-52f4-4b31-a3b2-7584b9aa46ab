package msg_dispatch

// OpSystemMsgRoomPushParam param
type OpSystemMsgRoomPushParam struct {
	RoomId      int64   `json:"room_id"`      //roomId=0 为全站广播消息
	Msg         string  `json:"msg"`          //消息内容
	Lan         string  `json:"lan"`          //语言
	IncludeMids []int64 `json:"include_mids"` //白名单用户IDs
	ExcludeMids []int64 `json:"exclude_mids"` //黑名单用户IDs
}

// OpSystemMsgRoomPush 通用推送系统直播间消息
func (d *MsgDispatch) OpSystemMsgRoomPush(param OpSystemMsgRoomPushParam) error {

	uri := "/msgdispatcher/httpapi/op_system_msg_room_push"
	respData := &PushRoomParamResp{}

	err := d.apiClient.DoPost(uri, param, respData)
	if err != nil {
		return err
	}

	err = respData.Error()
	if err != nil {
		return err
	}

	return nil
}

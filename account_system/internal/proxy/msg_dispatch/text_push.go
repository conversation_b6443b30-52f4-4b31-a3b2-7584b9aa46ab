package msg_dispatch

import (
	"zlutils/yaplay/api_client"
)

// TextPushParam params
type TextPushParam struct {
	Mid    int64  `json:"mid"`
	RoomID int64  `json:"room_id"`
	Msg    string `json:"msg"`
	AtMid  int64  `json:"at_mid"`
}

// TextPush push text
func (d *MsgDispatch) TextPush(param TextPushParam) error {

	uri := "/msgdispatcher/httpapi/text_push"
	respData := &api_client.ApiCode{}

	err := d.apiClient.DoPost(uri, param, respData)
	if err != nil {
		return err
	}

	err = respData.Error()
	if err != nil {
		return err
	}

	return nil
}

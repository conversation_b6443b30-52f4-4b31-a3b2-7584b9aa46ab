package msg_dispatch

import (
	"new-gitlab.xunlei.cn/funup/yaplay_proto/msgdispatcher"
	"zlutils/yaplay/api_client"
)

// PushUserParam params
type PushUserParam struct {
	Type     msgdispatcher.PushType `json:"type"`    //push msg type defined in msgdispatcher
	Mid      int64                  `json:"mid"`     // 要发给谁
	RoomId   int64                  `json:"room_id"` // 玩家所在的房间, 必填.如果玩家不在房间内则推送不到
	PushData interface{}            `json:"push_data"`
}

// PushUser push user
func (d *MsgDispatch) PushUser(param PushUserParam) error {

	uri := "/msgdispatcher/httpapi/push_user"
	respData := &api_client.ApiCode{}

	err := d.apiClient.DoPost(uri, param, respData)
	if err != nil {
		return err
	}

	err = respData.Error()
	if err != nil {
		return err
	}

	return nil
}

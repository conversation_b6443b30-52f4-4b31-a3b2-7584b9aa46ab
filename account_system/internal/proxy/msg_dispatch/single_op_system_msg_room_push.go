package msg_dispatch

// SingleOpSystemMsgRoomPushParam param
type SingleOpSystemMsgRoomPushParam struct {
	RoomId int64  `json:"room_id"`
	Msg    string `json:"msg"`  //消息内容
	Mid    int64  `json:"mids"` //用户ID
}

// SingleOpSystemMsgRoomPush 通用推送系统直播间消息 (单播)
func (d *MsgDispatch) SingleOpSystemMsgRoomPush(param SingleOpSystemMsgRoomPushParam) error {

	uri := "/msgdispatcher/httpapi/single_op_system_msg_room_push"
	respData := &PushRoomParamResp{}

	err := d.apiClient.DoPost(uri, param, respData)
	if err != nil {
		return err
	}

	err = respData.Error()
	if err != nil {
		return err
	}

	return nil
}

package locationapi

import (
	"context"
	"encoding/json"
	"fmt"
	"github.com/stretchr/testify/assert"
	"testing"
)

func TestLocationApi_GetLocationV2(t *testing.T) {
	SetUrlPrefix("https://api-in.imfunup.com")
	api := NewLocationApi(context.Background())

	respData, err := api.GetLocationV2("175.143.62.120")
	d, _ := json.Marshal(respData)
	fmt.Println(string(d))
	assert.Nil(t, err)
	assert.Equal(t, "MY", respData.Data.Country)
}

package locationapi

import (
	"context"
	"github.com/sirupsen/logrus"
	"zlutils/yaplay/api_client"
)

var urlPrefix string = "http://client-location:22004"

func SetUrlPrefix(prefix string) {
	urlPrefix = prefix
}

type LocationApi struct {
	api_client.ApiClient
}

func NewLocationApi(ctx context.Context) *LocationApi {
	return &LocationApi{
		ApiClient: api_client.ApiClient{
			UrlPrefix:  urlPrefix,
			Ctx:        ctx,
			HttpClient: api_client.ShortTimeoutRequestClient,
		},
	}
}

func NewLocationApiV2(ctx context.Context, url string) *LocationApi {
	return &LocationApi{
		ApiClient: api_client.ApiClient{
			UrlPrefix:  url,
			Ctx:        ctx,
			HttpClient: api_client.ShortTimeoutRequestClient,
		},
	}
}

type GetLocationRespData struct {
	api_client.ApiCode
	Data struct {
		Country      string `json:"country"`
		CountryId    int64  `json:"country_id"`
		ProvinceCode int64  `json:"province_code"`
		ProvinceName string `json:"province_name"`
		CityCode     int64  `json:"city_code"`
		CityName     string `json:"city_name"`
	} `json:"data"`
}

func (m *LocationApi) GetLocation(ip string) (resp *GetLocationRespData, err error) {
	uri := "/location/rpc/v1/ip/get_location"
	postData := &struct {
		Ip string `json:"ip"`
	}{
		Ip: ip,
	}
	resp = &GetLocationRespData{}
	err = m.DoPost(uri, postData, resp)
	if err != nil {
		logrus.WithContext(m.Ctx).WithFields(logrus.Fields{
			"func": "LocationApi.GetLocation",
			"ip":   ip,
		}).Errorf("do post failed. %s", err)
		return
	}

	err = resp.Error()
	if err != nil {
		logrus.WithContext(m.Ctx).WithFields(logrus.Fields{
			"func": "LocationApi.GetLocation",
			"ip":   ip,
		}).Errorf("api return error failed. %s", err)
		return
	}

	return
}

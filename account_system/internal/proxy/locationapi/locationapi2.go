package locationapi

import (
	"github.com/sirupsen/logrus"
	"zlutils/yaplay/api_client"
)

type GetLocationV2RespData struct {
	api_client.ApiCode
	Data struct {
		Country      Country `json:"country"`
		ProvinceName string  `json:"province_name"`
		CityName     string  `json:"city_name"`
	} `json:"data"`
}

// GetLocationV2 v2
func (m *LocationApi) GetLocationV2(ip string) (resp *GetLocationV2RespData, err error) {
	uri := "/location/rpc/v2/ip/get_location"
	postData := &struct {
		Ip string `json:"ip"`
	}{
		Ip: ip,
	}
	resp = &GetLocationV2RespData{}
	err = m.DoPost(uri, postData, resp)
	if err != nil {
		logrus.WithContext(m.Ctx).WithFields(logrus.Fields{
			"func": "LocationApi.GetLocationV2",
			"ip":   ip,
		}).Errorf("do post failed. %s", err)
		return
	}

	err = resp.Error()
	if err != nil {
		logrus.WithContext(m.Ctx).WithFields(logrus.Fields{
			"func": "LocationApi.GetLocationV2",
			"ip":   ip,
		}).Errorf("api return error failed. %s", err)
		return
	}

	return
}

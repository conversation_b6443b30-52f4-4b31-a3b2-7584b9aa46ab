package account

import (
	"context"
	"encoding/json"
	"fmt"
	system "new-gitlab.xunlei.cn/funup/yaplay_proto/account_system"
	"testing"
)

func TestAppSystemImp_SwitchGender(t *testing.T) {
	app := &AppSystemImp{}
	options := []option{
		withConfig(),
		withAccountDB(),
		withRedis(),
		withMongo,
		withCache,
	}
	for _, o := range options {
		o(app)
	}
	area, err := app.SwitchGender(context.Background(), &system.SwitchGenderReq{
		Mid:      *********,
		Gender:   1,
		Operator: "lijian",
	})
	if err != nil {
		return
	}
	data, _ := json.Marshal(area)
	fmt.Println(string(data))
}

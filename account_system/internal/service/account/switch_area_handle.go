package account

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"new-gitlab.xunlei.cn/funup/account_system/internal/proxy/trade"
	"strconv"
	"strings"
	"time"
	"zlutils/yaplay/grpc_err"

	"github.com/sirupsen/logrus"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"
	"new-gitlab.xunlei.cn/funup/account_system/internal/action"
	"new-gitlab.xunlei.cn/funup/account_system/internal/cache"
	"new-gitlab.xunlei.cn/funup/account_system/internal/config"
	"new-gitlab.xunlei.cn/funup/account_system/internal/dao"
	"new-gitlab.xunlei.cn/funup/account_system/internal/model"
	"new-gitlab.xunlei.cn/funup/account_system/internal/proxy/socicl_contact_grpc"
	"new-gitlab.xunlei.cn/funup/account_system/internal/proxy/welfare_center_grpc"
	"new-gitlab.xunlei.cn/funup/account_system/pkg/util"
	"new-gitlab.xunlei.cn/funup/i18n_sdk"
	********************* "new-gitlab.xunlei.cn/funup/yaplay_activity_proxy/hiya/chat_push"
	activityProxyGift "new-gitlab.xunlei.cn/funup/yaplay_activity_proxy/hiya/gift"
	activityProxyTrade "new-gitlab.xunlei.cn/funup/yaplay_activity_proxy/hiya/trade"
	activityProxyLib "new-gitlab.xunlei.cn/funup/yaplay_activity_proxy/lib"
	system "new-gitlab.xunlei.cn/funup/yaplay_proto/account_system"
	"new-gitlab.xunlei.cn/funup/yaplay_proto/social_contact"
	"new-gitlab.xunlei.cn/funup/yaplay_proto/welfare_center"
	welfare "new-gitlab.xunlei.cn/funup/yaplay_proto/welfare_center"
)

// 用户换区域
func (a *AppSystemImp) SwitchArea(ctx context.Context, req *system.SwitchAreaReq) (rsp *system.SwitchAreaRsp, err error) {
	logger := logrus.WithFields(logrus.Fields{
		"baseFunc": "SwitchArea",
		"req":      &req,
	})
	logger.Info("reqStart")

	errDetail := ""
	defer func() {
		if err != nil || errDetail != "" {
			logger.WithFields(logrus.Fields{
				"deferErr":  err,
				"errDetail": errDetail,
			}).Error()
		}
	}()

	if req.GetMid() == 0 || (req.GetArea() != "ES" && req.GetArea() != "AR" && req.GetArea() != "TR") || req.GetOperator() == "" {
		return nil, grpc_err.GrpcErrBadParams
	}

	message := ""
	rsp, message, errDetail, err = a.switchArea(ctx, req, logger)
	if message != "" && errDetail == "" {
		errDetail = message
	}

	if err != nil {
		return nil, grpc_err.GrpcWrapInternal(err)
	}
	if message != "" {
		return nil, status.New(codes.Unknown, message).Err()
	}
	return rsp, nil
}

func (a *AppSystemImp) switchArea(ctx context.Context, req *system.SwitchAreaReq, logger *logrus.Entry) (rsp *system.SwitchAreaRsp, messages string, errDetail string, err error) {
	rsp = &system.SwitchAreaRsp{}
	mid := req.GetMid()
	userArea := req.GetArea()

	mapArea := map[string]string{
		"CS": "华语区",
		"ES": "英语区",
		"AR": "中东区",
		"KR": "东亚区",
		"VN": "越南区",
		"TR": "土耳其区",
	}

	area, err := a.getUserArea(ctx, mid)
	if err != nil {
		logger.Errorf("getUserArea error = %v", err)
		return
	}

	if area == userArea {
		messages = fmt.Sprintf("该用户已经是%s", mapArea[userArea])
		return
	}

	if !activityProxyLib.IsInIntArr(config.BaseConfigFormat.AreaWhiteList, mid) {
		if errT := a.checkAnchorTask(ctx, mid); errT != nil {
			messages = errT.Error()
			return rsp, messages, errDetail, err
		}

		// 获取用户的dt和appver
		memberExtDao := dao.NewMemberExtDao(a.mongoClient)
		v2, err := memberExtDao.FindOneV2(ctx, req.GetMid())
		if err != nil {
			logger.Errorf("FindOneV2 error = %v", err)
			return rsp, messages, errDetail, err
		}

		logrus.WithContext(ctx).Infof("FindOneV2 v2 %+v", v2)

		// todo 用户版本低于1880不允许换区
		if !util.IsHigherVer(v2.AppVer, v2.Dt, "10.9.3.1880", "10.9.3.1880") {
			messages = fmt.Sprintf("用户版本过低，请提示用户升级后再操作切区")
			return rsp, messages, errDetail, err
		}

		/*
			会长换区：会长不允许换区，若身份为会长，在后台操作换区时提示“该用户是XXX公会会长，不可换区”
			主播换区：有公会的主播换区后，需要自动移除出公会，走公会长移除会员的流程；在后台操作换区时提示“该用户是XXX公会成员，操作后会移除出公会，请与公会长和主播确认后操作”
		*/
		guildCacheByMid := cache.NewGuildCacheUseCase()
		guildInfoByMidData := guildCacheByMid.GetGuildInfoByMid(mid)
		if guildInfoByMidData.Joined &&
			!activityProxyLib.IsInIntArr(config.BaseConfigFormat.GuildAreaWhiteList, guildInfoByMidData.Info.Id) {
			// 获取美金
			buyClient := trade.NewTradeApiClient(context.Background(), config.RemoteApiConfig.BuyUrl)
			usdMap, uErr := buyClient.GetUserUsd([]int64{mid})
			if uErr != nil || usdMap == nil {
				logger.WithFields(logrus.Fields{
					"func": "buyClient_GetUserUsd",
					"err":  uErr,
				}).Error()
				return rsp, messages, errDetail, uErr
			}
			usdMsg := ""
			if usdMap[mid] > 0 {
				usdMsg = fmt.Sprintf("有%d美金账户资产，", usdMap[mid])
			}
			if guildInfoByMidData.Info.Owner.Id == mid {
				messages = fmt.Sprintf("该用户是%s公会会长，%s不可换区", guildInfoByMidData.Info.Name, usdMsg)
				return rsp, messages, errDetail, err
			}
			messages = fmt.Sprintf("该用户是%s公会成员，%s请与公会长和主播确认后，先移除出公会，再换区", guildInfoByMidData.Info.Name, usdMsg)
			return rsp, messages, errDetail, err
		}

		/*
			家族长换区：无法换区，提示“用户为IDXXX家族的家族长，无法换区”；需要运营和公会长沟通解散后再进行换区
			管理员、普通成员：管理员或普通成员，提示“用户为IDXXX家族的家族成员，操作后用户将自动退出家族，请谨慎操作”，二次确认后换区成功，且自动为用户走主动退出家族的流程
		*/
		familyBaseServiceClient := socicl_contact_grpc.GetFamilyBaseServiceClient()
		familyInfo := new(social_contact.FamilyInfo)
		if familyBaseServiceClient != nil {
			familyInfoRsp, errF := familyBaseServiceClient.BatchFamilyInfoByMids(ctx, &social_contact.BatchFamilyInfoByMidsReq{
				HM:          mid,
				Mids:        []int64{mid},
				HAreaRegion: area,
				HLanguage:   req.GetHLanguage(),
			})
			if errF != nil {
				logger.Errorf("BatchFamilyInfoByMids error = %v", errF)
				messages = errF.Error()
				return rsp, messages, errDetail, err
			} else if familyInfoRsp != nil {
				if v, ok := familyInfoRsp.GetFamilyInfos()[mid]; ok {
					familyInfo = v
				}
			}
		}
		if familyInfo != nil && familyInfo.GetId() != 0 { // 用户在家族中
			if familyInfo.GetPatriarchId() == mid { // 家族长
				messages = fmt.Sprintf("用户为ID%d家族的家族长，无法换区", familyInfo.GetId())
				return rsp, messages, errDetail, err
			} else { // 家族其他成员：退会
				//messages = fmt.Sprintf("用户为ID%d家族的家族成员，操作后用户将自动退出家族，请谨慎操作", familyInfo.GetId())
				if familyBaseServiceClient != nil {
					_, errQ := familyBaseServiceClient.QuitFamilyMember(ctx, &social_contact.QuitFamilyMemberReq{
						HM:          mid,
						FamilyId:    familyInfo.GetId(),
						HLanguage:   req.GetHLanguage(),
						HAreaRegion: area,
					})
					if errQ != nil {
						logger.Errorf("QuitFamilyMember error = %v", errQ)
						messages = errQ.Error()
						return rsp, messages, errDetail, err
					}
					logger.Infof("QuitFamilyMember familyId: %+v, mid: %+v", familyInfo.GetId(), mid)
				}
			}
		}
	}

	// 账号有价值，背包里有余量价值时（包括金币、钻石、背包礼包、价值道具等、贵族币），技术侧限制切区，切区时toast“该账号不满足切区条件”
	// 获取用户的资产
	tradeClient := activityProxyTrade.NewApiClient(context.Background(), config.RemoteApiConfig.TradeUrl)
	tradeInfoRsp, err := tradeClient.GetUserTradeInfo(mid, activityProxyLib.BaseParm{})
	if err != nil || tradeInfoRsp == nil {
		logger.WithFields(logrus.Fields{
			"func": "tradeClient_GetUserTradeInfo",
			"err":  err,
		}).Error()
		return
	}

	// 获取用户的背包礼物
	giftClient := activityProxyGift.NewApiClient(context.Background(), config.RemoteApiConfig.GiftUrl)
	bagListRsp, err := giftClient.GiftsBagList(mid)
	if err != nil || bagListRsp == nil {
		logger.WithFields(logrus.Fields{
			"func": "giftClient_GiftsBagList",
			"err":  err,
		}).Error()
		return
	}

	// 获取用户的卡券
	getCardsByCostRspListLen := 0
	propServiceClient := welfare_center_grpc.GetPropServiceClient()
	if propServiceClient != nil {
		getCardsByCostRsp, err := propServiceClient.GetCardsByCost(context.Background(), &welfare_center.GetCardsByCostReq{
			HM: mid,
		})
		if err != nil || getCardsByCostRsp == nil {
			logger.WithFields(logrus.Fields{
				"func": "propServiceClient_GetCardsByCost",
				"err":  err,
			}).Error()
		} else {
			getCardsByCostRspListLen = len(getCardsByCostRsp.GetList())
		}
	}

	if !req.GetIsDirect() && (tradeInfoRsp.Data.AllCoins > 0 || len(bagListRsp.Data.Gifts) > 0 || getCardsByCostRspListLen > 0) {
		rsp.ConfirmText = "用户有可用资产，换区后影响分成，请注意核实后再操作换区"
		return
	}

	// 需要把用户的积分，回刷到原金币字段
	userChangeDao := dao.NewUserChangeDao(a.accountDB, a.relationDB)
	err = userChangeDao.UpdateUserCoin(mid, userArea)
	if err != nil {
		logger.WithFields(logrus.Fields{
			"func": "userChangeDao_UpdateUserCoin",
			"err":  err,
		}).Error()
		return
	}

	err = a.saveUserArea(ctx, mid, userArea)
	if err != nil {
		return
	}
	a.setAreaCache(ctx, mid, userArea)

	// 清除me_ext缓存
	key := fmt.Sprintf("me_ext_%d", mid)
	a.rc.Del(ctx, key)

	// 新人活跃任务切大区处理
	go func() {
		defer func() {
			if r := recover(); r != nil {
				logger.Errorf("newbie switch area, panic: %v", r)
			}
		}()
		_, _ = welfare_center_grpc.GetTaskServiceClient().NewbieSwitchArea(context.Background(), &welfare.NewbieSwitchAreaReq{
			HM: mid,
		})
	}()

	// 更新用户属性
	action.AddActionLogV2(mid,
		&action.Action{Action: "update", Otype: "w_area"},
		map[string]interface{}{
			"w_area": userArea,
		})

	content, hit := i18n_sdk.GetMultiCopyWritingByMID("正在为您进行服务升级，请刷新内容", mid, nil)
	if !hit {
		content = "正在为您进行服务升级，请刷新内容"
	}
	// 给用户下发消息
	type MsgObj struct {
		Content string `json:"content"`
	}
	msg := MsgObj{
		Content: content,
	}
	buf, err := json.Marshal(msg)
	if err != nil {
		return
	}
	chatPushClient := *********************.NewApiClient(context.Background(), config.RemoteApiConfig.ChatPushUrl)
	chatPushResp, errRes := chatPushClient.Notification(*********************.NotificationParams{
		Apps:    []string{"me-live"},
		FromMid: 1,
		ToMid:   mid,
		Type:    3300,
		Subtype: 3300,
		Src:     buf,
		Ct:      time.Now().Unix(),
	})
	if errRes != nil || chatPushResp == nil {
		logrus.WithFields(logrus.Fields{
			"func": "chatPushClient.Notification",
			"err":  errRes,
		}).Error()
	} else {
		logrus.WithFields(logrus.Fields{
			"func":    "chatPushClient.Notification",
			"FromMid": 1,
			"ToMid":   mid,
			"Type":    3300,
			"Subtype": 3300,
			"Src":     buf,
		}).Info()
	}

	// 记录变更
	_ = userChangeDao.UserAreaListCreate(model.UserAreaList{
		Mid:            mid,
		Area:           userArea,
		UpdateDatetime: time.Now(),
		Operator:       req.GetOperator(),
	})

	// todo 房间的归属地也更新
	roomDao := dao.NewRoomDao(a.accountDB)
	roomDao.UpdateUserRoomArea(ctx, mid, userArea)
	return
}

func (a *AppSystemImp) checkAnchorTask(ctx context.Context, targetMid int64) error {
	anchorTaskDao := dao.NewAnchorTaskDao(a.slaveDB)
	tasks, err := anchorTaskDao.FindTaskListByMid(ctx)
	if err != nil {
		return err
	}
	for _, task := range tasks {
		midList := StringToIntArr(task.WhiteMids)
		for _, mid := range midList {
			if targetMid == mid {
				msg := fmt.Sprintf("用户[%d]正在进行任务台任务，任务ID[%d],任务名称[%s],请修改", targetMid, task.TaskID, task.TaskName)
				return errors.New(msg)
			}
		}
	}

	return nil
}

func StringToIntArr(numbersStr string) []int64 {
	if numbersStr == "" {
		return make([]int64, 0)
	}
	numberStrs := strings.Split(numbersStr, ",")
	numbers := make([]int64, len(numberStrs))
	for i, str := range numberStrs {
		num, err := strconv.ParseInt(str, 10, 64)
		if err != nil {
			fmt.Printf("Failed to parse number %s: %s\n", str, err)
		}
		numbers[i] = num
	}
	return numbers
}

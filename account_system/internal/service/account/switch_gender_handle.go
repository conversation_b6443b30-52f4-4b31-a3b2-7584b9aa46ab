package account

import (
	"context"
	"fmt"
	"github.com/sirupsen/logrus"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"
	"new-gitlab.xunlei.cn/funup/account_system/internal/dao"
	system "new-gitlab.xunlei.cn/funup/yaplay_proto/account_system"
)

// SwitchGender 更换性别
func (a *AppSystemImp) SwitchGender(ctx context.Context, req *system.SwitchGenderReq) (rsp *system.SwitchGenderRsp, err error) {
	// 日志
	logger := logrus.WithFields(logrus.Fields{
		"baseFunc": "SwitchGender",
		"req":      &req,
	})
	logger.Info("reqStart")

	errDetail := ""
	defer func() {
		if err != nil || errDetail != "" {
			logger.WithFields(logrus.Fields{
				"deferErr":  err,
				"errDetail": errDetail,
			}).Error()
		}
	}()

	errMessages := ""
	rsp = &system.SwitchGenderRsp{}
	errMessages, err = a.switchGender(ctx, req, logger)

	if err != nil {
		return rsp, err
	}
	if errMessages != "" {
		return rsp, status.New(codes.InvalidArgument, errMessages).Err()
	}

	return rsp, nil
}

func (a *AppSystemImp) switchGender(ctx context.Context, req *system.SwitchGenderReq, logger *logrus.Entry) (errMessages string, err error) {
	mid := req.GetMid()
	userGender := req.GetGender()
	operator := req.GetOperator()
	mapGender := map[int64]string{
		0: "未知",
		1: "女",
		2: "男",
	}

	// 参数校验
	_, isGender := mapGender[userGender]
	if mid == 0 || !isGender || operator == "" {
		errMessages = fmt.Sprintf("请求参数错误: gender:%d, operator:%s,mid:%d", userGender, operator, mid)
		return
	}

	// 是当前性别
	gender, err := a.getUserGender(ctx, mid)
	if err != nil {
		logger.Errorf("getUserGender error = %v, Operator: %s", err, operator)
		return
	}
	if gender == userGender {
		logger.Infof("用户性别已经是%s, Operator: %s", mapGender[userGender], operator)
		return
	}

	// 保存修改
	err = a.saveUserGender(ctx, mid, userGender)
	if err != nil {
		return
	}

	// 清除缓存
	key := fmt.Sprintf("me_%d", mid)
	a.rc.Del(ctx, key)

	return
}

// 获取用户性别
func (a *AppSystemImp) getUserGender(ctx context.Context, mid int64) (gender int64, err error) {
	log := logrus.WithContext(ctx).WithFields(logrus.Fields{
		"func": "getUserGender",
		"mid":  mid,
	})

	// 查mongo
	memberDao := dao.NewMemberDao(a.mongoClient)
	member, err := memberDao.FindOne(ctx, mid)
	if err != nil {
		log.Errorf("error = %v", err)
		return 0, err
	}
	gender = member.Gender

	return gender, nil
}

// 保存性别修改
func (a *AppSystemImp) saveUserGender(ctx context.Context, mid int64, gender int64) error {
	log := logrus.WithContext(ctx).WithFields(logrus.Fields{
		"func": "saveUserGender",
		"mid":  mid,
	})
	memberDao := dao.NewMemberDao(a.mongoClient)
	err := memberDao.UpdateOne(ctx, mid, gender)
	if err != nil {
		log.Errorf("memberDao UpdateOne error = %v", err)
		return err
	}
	return nil
}

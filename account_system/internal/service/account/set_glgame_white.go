package account

import (
	"context"
	"fmt"

	"github.com/sirupsen/logrus"
	"new-gitlab.xunlei.cn/funup/account_system/internal/dao"
	"new-gitlab.xunlei.cn/funup/account_system/internal/model"
	system "new-gitlab.xunlei.cn/funup/yaplay_proto/account_system"
)

func (a *AppSystemImp) BatchSetGlGameWhite(ctx context.Context, req *system.BatchSetGlGameWhiteReq) (rsp *system.BatchSetGlGameWhiteRsp, err error) {
	if len(req.GetMids()) == 0 {
		return
	}
	logrus.Infof("BatchSetGlGameWhite req: %+v", req)
	rsp = &system.BatchSetGlGameWhiteRsp{}
	if len(req.GetMids()) > 20 {
		return nil, fmt.Errorf("mids too many")
	}
	whiteDao := dao.NewGlgameWhiteDao(a.accountDB)
	err = whiteDao.BatchSetGlgameWhite(req.GetMids(), "")
	if err != nil {
		logrus.Errorf("BatchSetGlGameWhite error = %v req: %+v", err, req)
		return rsp, err
	}
	rsp.Whites = req.GetMids()
	return
}

func (a *AppSystemImp) BatchGetGlgameWhite(ctx context.Context, req *system.BatchGetGlGameWhiteReq) (rsp *system.BatchGetGlGameWhiteRsp, err error) {
	if len(req.GetMids()) == 0 {
		return
	}
	logrus.Infof("BatchGetGlGameWhite req: %+v", req)
	rsp = &system.BatchGetGlGameWhiteRsp{}
	if len(req.GetMids()) > 20 {
		return nil, fmt.Errorf("mids too many")
	}
	whiteDao := dao.NewGlgameWhiteDao(a.accountDB)
	white, err1 := whiteDao.BatchGetGlgameWhite(req.GetMids())
	if err1 != nil {
		logrus.Errorf("BatchGetGlgameWhite error = %v req: %+v", err1, req)
		return rsp, err
	}
	if white == nil {
		return
	}
	whites := make(map[int64]string)
	for _, w := range white {
		if w.State == model.GlgameWhiteStateDelete {
			continue
		}
		whites[w.Mid] = w.GlGame
	}
	logrus.Infof("BatchSetGlGameWhite rsp: %+v", rsp)
	rsp.Whites = whites
	return
}

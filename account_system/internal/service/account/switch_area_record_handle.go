package account

import (
	"context"
	"github.com/sirupsen/logrus"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"
	"new-gitlab.xunlei.cn/funup/account_system/internal/dao"
	system "new-gitlab.xunlei.cn/funup/yaplay_proto/account_system"
	"zlutils/yaplay/grpc_err"
)

// 用户换区域的记录
func (a *AppSystemImp) SwitchAreaRecord(ctx context.Context, req *system.SwitchAreaRecordReq) (rsp *system.SwitchAreaRecordRsp, err error) {
	logger := logrus.WithFields(logrus.Fields{
		"baseFunc": "SwitchAreaRecord",
		"req":      &req,
	})
	logger.Info("reqStart")

	errDetail := ""
	defer func() {
		if err != nil || errDetail != "" {
			logger.WithFields(logrus.Fields{
				"deferErr":  err,
				"errDetail": errDetail,
			}).Error()
		}
	}()

	if req.GetMid() == 0 {
		return nil, grpc_err.GrpcErrBadParams
	}

	message := ""
	rsp, message, errDetail, err = a.switchAreaRecord(ctx, req, logger)
	if message != "" && errDetail == "" {
		errDetail = message
	}

	if err != nil {
		return nil, grpc_err.GrpcWrapInternal(err)
	}
	if message != "" {
		return nil, status.New(codes.Unknown, message).Err()
	}
	return rsp, nil
}

func (a *AppSystemImp) switchAreaRecord(ctx context.Context, req *system.SwitchAreaRecordReq, logger *logrus.Entry) (rsp *system.SwitchAreaRecordRsp, messages string, errDetail string, err error) {
	rsp = &system.SwitchAreaRecordRsp{}

	userChangeDao := dao.NewUserChangeDao(a.accountDB, a.relationDB)
	userAreaLists, err := userChangeDao.UserAreaListQuery(req.GetMid())
	if err != nil {
		return
	}

	list := make([]*system.SwitchAreaRecordItem, 0)
	for _, userArea := range userAreaLists {
		list = append(list, &system.SwitchAreaRecordItem{
			Area:       userArea.Area,
			UpdateTime: userArea.UpdateDatetime.Unix(),
			Operator:   userArea.Operator,
		})
	}

	rsp.List = list
	return
}

package account

import (
	"context"
	"fmt"
	"github.com/sirupsen/logrus"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"
	"new-gitlab.xunlei.cn/funup/account_system/internal/dao"
	"new-gitlab.xunlei.cn/funup/account_system/internal/proxy/welfare_center_grpc"
	system "new-gitlab.xunlei.cn/funup/yaplay_proto/account_system"
	welfare "new-gitlab.xunlei.cn/funup/yaplay_proto/welfare_center"
	"zlutils/yaplay/grpc_err"
)

// 用户换归属地
func (a *AppSystemImp) SwitchCountry(ctx context.Context, req *system.SwitchCountryReq) (rsp *system.SwitchCountryRsp, err error) {
	logger := logrus.WithFields(logrus.Fields{
		"baseFunc": "SwitchCountry",
		"req":      &req,
	})
	logger.Info("reqStart")

	errDetail := ""
	defer func() {
		if err != nil || errDetail != "" {
			logger.WithFields(logrus.Fields{
				"deferErr":  err,
				"errDetail": errDetail,
			}).Error()
		}
	}()

	if req.GetMid() == 0 || req.GetCountry() == "" || req.GetOperator() == "" {
		return nil, grpc_err.GrpcErrBadParams
	}

	message := ""
	rsp, message, errDetail, err = a.switchCountry(ctx, req, logger)
	if message != "" && errDetail == "" {
		errDetail = message
	}

	if err != nil {
		return nil, grpc_err.GrpcWrapInternal(err)
	}
	if message != "" {
		return nil, status.New(codes.Unknown, message).Err()
	}
	return rsp, nil
}

func (a *AppSystemImp) switchCountry(ctx context.Context, req *system.SwitchCountryReq, logger *logrus.Entry) (rsp *system.SwitchCountryRsp, messages string, errDetail string, err error) {
	rsp = &system.SwitchCountryRsp{}

	defer func() {
		// 新人活跃任务切大区处理
		go func() {
			defer func() {
				if r := recover(); r != nil {
					logger.Errorf("newbie switch area, panic: %v", r)
				}
			}()
			_, _ = welfare_center_grpc.GetTaskServiceClient().NewbieSwitchArea(context.Background(), &welfare.NewbieSwitchAreaReq{
				HM: req.GetMid(),
			})
		}()
	}()
	memberExtDao := dao.NewMemberExtDao(a.mongoClient)
	err = memberExtDao.UpdateCountryOne(ctx, req.GetMid(), req.GetCountry())
	if err != nil {
		logger.Errorf("memberExtDao UpdateCountryOne error = %v", err)
		return
	}

	key := fmt.Sprintf("area:info:%v", req.GetMid())
	a.rcDB1.Del(ctx, key)

	/*
		// 记录变更
		userChangeDao := dao.NewUserChangeDao(a.accountDB, a.relationDB)
		_ = userChangeDao.UserCountryListCreate(model.UserCountryList{
			Mid:            req.GetMid(),
			Country:        req.GetCountry(),
			UpdateDatetime: time.Now(),
			Operator:       req.GetOperator(),
		})
	*/
	return
}

package account

import (
	"context"
	"github.com/sirupsen/logrus"
	system "new-gitlab.xunlei.cn/funup/yaplay_proto/account_system"
)

// ResetInitBelongArea 初始化账号归属 interface
func (a *AppSystemImp) ResetInitBelongArea(ctx context.Context,
	req *system.ResetInitBelongAreaReq) (*system.ResetInitBelongAreaRsp, error) {
	log := logrus.WithContext(ctx).WithFields(logrus.Fields{
		"func": "ResetInitBelongArea",
		"req":  req,
	})
	log.Infof("ResetInitBelongArea interface")
	rsp := new(system.ResetInitBelongAreaRsp)
	if err := a.resetInitBelongArea(ctx, req, rsp); err != nil {
		log.Errorf("init belong area error = %v", err)
		return nil, err
	}
	log.Infof("rsp = %v", rsp)
	return rsp, nil
}

// resetInitBelongArea 重置用户大区信息
func (a *AppSystemImp) resetInitBelongArea(ctx context.Context,
	req *system.ResetInitBelongAreaReq, rsp *system.ResetInitBelongAreaRsp) error {
	log := logrus.WithContext(ctx).WithFields(logrus.Fields{
		"func": "resetInitBelongArea_handle",
		"req":  req,
	})
	initBelongAreaReq := &system.InitBelongAreaReq{
		HM:            req.GetHM(),
		HCarrier:      req.GetHCarrier(),
		HLanguage:     req.HLanguage,
		HDid:          req.GetHDid(),
		ResetClientIp: req.GetResetClientIp(),
		HDt:           req.GetHDt(),
		HAv:           req.GetHAv(),
	}
	initBelongAreaRsp := &system.InitBelongAreaRsp{}
	err := a.initBelongArea(ctx, initBelongAreaReq, initBelongAreaRsp)
	if err != nil {
		log.Errorf("initBelongArea error = %v", err)
		return err
	}
	rsp.Area = initBelongAreaRsp.GetArea()
	return nil
}

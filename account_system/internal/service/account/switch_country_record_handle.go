package account

import (
	"context"
	"github.com/sirupsen/logrus"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"
	system "new-gitlab.xunlei.cn/funup/yaplay_proto/account_system"
	"zlutils/yaplay/grpc_err"
)

// 用户换归属地的记录
func (a *AppSystemImp) SwitchCountryRecord(ctx context.Context, req *system.SwitchCountryRecordReq) (rsp *system.SwitchCountryRecordRsp, err error) {
	logger := logrus.WithFields(logrus.Fields{
		"baseFunc": "SwitchCountryRecord",
		"req":      &req,
	})
	logger.Info("reqStart")

	errDetail := ""
	defer func() {
		if err != nil || errDetail != "" {
			logger.WithFields(logrus.Fields{
				"deferErr":  err,
				"errDetail": errDetail,
			}).Error()
		}
	}()

	if req.GetMid() == 0 {
		return nil, grpc_err.GrpcErrBadParams
	}

	message := ""
	rsp, message, errDetail, err = a.switchCountryRecord(ctx, req, logger)
	if message != "" && errDetail == "" {
		errDetail = message
	}

	if err != nil {
		return nil, grpc_err.GrpcWrapInternal(err)
	}
	if message != "" {
		return nil, status.New(codes.Unknown, message).Err()
	}
	return rsp, nil
}

func (a *AppSystemImp) switchCountryRecord(ctx context.Context, req *system.SwitchCountryRecordReq, logger *logrus.Entry) (rsp *system.SwitchCountryRecordRsp, messages string, errDetail string, err error) {
	rsp = &system.SwitchCountryRecordRsp{}
	/*
		userChangeDao := dao.NewUserChangeDao(a.accountDB, a.relationDB)
		userCountryLists, err := userChangeDao.UserCountryListQuery(req.GetMid())
		if err != nil {
			return
		}
	*/
	list := make([]*system.SwitchCountryRecordItem, 0)
	/*
		for _, userArea := range userCountryLists {
			list = append(list, &system.SwitchCountryRecordItem{
				Country:    userArea.Country,
				UpdateTime: userArea.UpdateDatetime.Unix(),
				Operator:   userArea.Operator,
			})
		}
	*/

	rsp.List = list
	return
}

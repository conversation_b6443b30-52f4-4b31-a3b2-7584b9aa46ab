package account

import (
	"context"
	"fmt"

	"github.com/sirupsen/logrus"
	"new-gitlab.xunlei.cn/funup/account_system/internal/model"
	accountSdk "new-gitlab.xunlei.cn/funup/base_sdk/account"
	system "new-gitlab.xunlei.cn/funup/yaplay_proto/account_system"
)

const (
	MaxParams = 20
)

// RPCBelongArea 账号归属区域 interface
func (a *AppSystemImp) RPCBelongArea(ctx context.Context,
	req *system.RPCBelongAreaReq) (resp *system.RPCBelongAreaRsp, err error) {
	log := logrus.WithContext(ctx).WithFields(logrus.Fields{
		"func": "RPCBelongArea",
		"req":  req,
	})
	log.Infof("RPCBelongArea interface")
	rsp := new(system.BelongAreaRsp)

	if err = a.belongArea(ctx, &system.BelongAreaReq{
		HM: req.GetHM(),
	}, rsp); err != nil {
		log.Errorf("RPCBelongArea error = %v", err)
		return nil, err
	}
	log.Infof("rsp = %v", rsp)

	if rsp.GetArea() == "" {
		rsp.Area = "CS" // 如果为空，一定是华语区的
	}

	return &system.RPCBelongAreaRsp{
		Area:           rsp.GetArea(),
		CoinRateConfig: rsp.GetCoinRateConfig(),
	}, nil
}

// RPCBatchBelongArea 批量获取账号归属 interface
func (a *AppSystemImp) RPCBatchBelongArea(ctx context.Context,
	req *system.RPCBatchBelongAreaReq) (resp *system.RPCBatchBelongAreaRsp, err error) {
	log := logrus.WithContext(ctx).WithFields(logrus.Fields{
		"func": "RPCBatchBelongArea",
		"req":  req,
	})
	log.Infof("RPCBatchBelongArea interface")

	// 参数校验
	if len(req.GetMids()) == 0 {
		err = fmt.Errorf("invalid params")
		return
	}
	if len(req.GetMids()) > MaxParams {
		err = fmt.Errorf("params exceed max")
		return
	}

	userAreaMap := make(map[int64]string)
	for _, mid := range req.GetMids() {
		rsp := new(system.BelongAreaRsp)
		if err = a.belongArea(ctx, &system.BelongAreaReq{
			HM: mid,
		}, rsp); err != nil {
			log.Errorf("RPCBelongAreaByMids error = %v", err)
			return nil, err
		}

		if rsp.GetArea() == "" {
			rsp.Area = "CS" // 如果为空，一定是华语区的
		}

		userAreaMap[mid] = rsp.Area
	}

	coinRateConfig := &system.BatchCoinRateConfig{}
	coinRateConfig.UsdToCoinPointRate = model.USD_TO_COIN_POINT_RATE
	coinRateConfig.CoinToCoinPointRate = model.COIN_TO_COIN_POINT_RATE
	coinRateConfig.ECoinToCoinPointRate = model.E_COIN_TO_COIN_POINT_RATE
	pointRateMap := make(map[int64]int64, 0)
	for areaMid, areaOne := range userAreaMap {
		pointRateMap[areaMid] = accountSdk.GetCoinRateByArea(areaOne)
	}
	coinRateConfig.PointRateMap = pointRateMap

	return &system.RPCBatchBelongAreaRsp{
		Areas:          userAreaMap,
		CoinRateConfig: coinRateConfig,
	}, nil
}

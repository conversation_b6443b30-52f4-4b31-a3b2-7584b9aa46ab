package account

import (
	"context"
	"encoding/json"
	"fmt"
	"google.golang.org/grpc/metadata"
	system "new-gitlab.xunlei.cn/funup/yaplay_proto/account_system"
	"testing"
)

func TestAppSystemImp_ResetInitBelongArea(t *testing.T) {
	app := &AppSystemImp{}
	options := []option{
		withConfig(),
		withAccountDB(),
		withRedis(),
		withAreaRedis,
		withMongo,
		withCache,
		withSlavDB(),
	}
	for _, o := range options {
		o(app)
	}
	type contextKey string
	ctx := context.Background()
	md := metadata.New(map[string]string{
		// 新加坡IP
		//"x-forwarded-for": "*************",
		// 加拿大IP
		//"x-forwarded-for": "************",
		// 柬埔寨
		//"x-forwarded-for": "***************",
		// 香港
		//"x-forwarded-for": "***************",
		// 台湾
		//"x-forwarded-for": "***********",
		// 菲律宾
		//"x-forwarded-for": "*************",
	})
	ctx = metadata.NewIncomingContext(ctx, md)
	area, err := app.ResetInitBelongArea(ctx, &system.ResetInitBelongAreaReq{
		HM:            *********,
		HCarrier:      "--,--,**********;--,--,**********",
		HLanguage:     "zh-Hant",
		HDid:          "",
		ResetClientIp: "*************",
		HDt:           0,
		HAv:           "10.9.4.1950",
	})
	if err != nil {
		return
	}
	data, _ := json.Marshal(area)
	fmt.Println(string(data))
}

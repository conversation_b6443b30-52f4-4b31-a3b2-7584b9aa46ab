package dao

import (
	"context"
	"time"

	"new-gitlab.xunlei.cn/funup/account_system/internal/model"

	"gorm.io/gorm"
)

// DeviceLevelRecordDAO 设备分级记录DAO
type DeviceLevelRecordDAO struct {
	db *gorm.DB
}

// NewDeviceLevelRecordDAO 创建设备分级记录DAO实例
func NewDeviceLevelRecordDAO(db *gorm.DB) *DeviceLevelRecordDAO {
	return &DeviceLevelRecordDAO{db: db}
}

// SaveOrUpdate 保存或更新设备分级记录
func (d *DeviceLevelRecordDAO) SaveOrUpdate(ctx context.Context, record *model.DeviceLevelRecord) error {
	if record == nil {
		return nil
	}

	// 设置更新时间
	record.UpdateTime = time.Now()

	// 使用原生SQL实现ON DUPLICATE KEY UPDATE
	sql := `
		INSERT INTO cn_me_account.device_level_record (
			h_did, h_dt, h_os, h_model, h_appid, h_av, h_m, h_ch, h_carrier, h_area_region,
			cpu_brand, cpu_core_count, cpu_frequency, memory_capacity, screen_width, screen_height,
			android_api_level, net_type, memory_remain_capacity, device_level, score, update_time, create_time, err_msg
		) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
		ON DUPLICATE KEY UPDATE
			h_dt = VALUES(h_dt),
			h_os = VALUES(h_os),
			h_model = VALUES(h_model),
			h_appid = VALUES(h_appid),
			h_av = VALUES(h_av),
			h_m = VALUES(h_m),
			h_ch = VALUES(h_ch),
			h_carrier = VALUES(h_carrier),
			h_area_region = VALUES(h_area_region),
			cpu_brand = VALUES(cpu_brand),
			cpu_core_count = VALUES(cpu_core_count),
			cpu_frequency = VALUES(cpu_frequency),
			memory_capacity = VALUES(memory_capacity),
			screen_width = VALUES(screen_width),
			screen_height = VALUES(screen_height),
			android_api_level = VALUES(android_api_level),
			net_type = VALUES(net_type),
			memory_remain_capacity = VALUES(memory_remain_capacity),
			device_level = VALUES(device_level),
			score = VALUES(score),
			update_time = VALUES(update_time),
			err_msg = VALUES(err_msg)
	`

	// 如果是新记录，设置创建时间
	if record.CreateTime.IsZero() {
		record.CreateTime = time.Now()
	}

	return d.db.WithContext(ctx).Exec(sql,
		record.HDid, record.HDt, record.HOs, record.HModel, record.HAppid, record.HAv,
		record.HM, record.HCh, record.HCarrier, record.HAreaRegion,
		record.CpuBrand, record.CpuCoreCount, record.CpuFrequency, record.MemoryCapacity,
		record.ScreenWidth, record.ScreenHeight, record.AndroidApiLevel, record.NetType,
		record.MemoryRemainCapacity, record.DeviceLevel, record.Score, record.UpdateTime, record.CreateTime, record.ErrMsg,
	).Error
}

// GetByDeviceID 根据设备ID获取设备分级记录
func (d *DeviceLevelRecordDAO) GetByDeviceID(ctx context.Context, deviceID string) (*model.DeviceLevelRecord, error) {
	var record model.DeviceLevelRecord
	err := d.db.WithContext(ctx).Where("h_did = ?", deviceID).First(&record).Error
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, nil
		}
		return nil, err
	}
	return &record, nil
}

// GetByDeviceIDAndOS 根据设备ID和操作系统获取设备分级记录
func (d *DeviceLevelRecordDAO) GetByDeviceIDAndOS(ctx context.Context, deviceID, os string) (*model.DeviceLevelRecord, error) {
	var record model.DeviceLevelRecord
	err := d.db.WithContext(ctx).Where("h_did = ? AND h_os = ?", deviceID, os).First(&record).Error
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, nil
		}
		return nil, err
	}
	return &record, nil
}

// GetDeviceLevelStats 获取设备分级统计信息
func (d *DeviceLevelRecordDAO) GetDeviceLevelStats(ctx context.Context) (map[int32]int64, error) {
	var results []struct {
		DeviceLevel int32 `json:"device_level"`
		Count       int64 `json:"count"`
	}

	err := d.db.WithContext(ctx).
		Model(&model.DeviceLevelRecord{}).
		Select("device_level, COUNT(*) as count").
		Group("device_level").
		Find(&results).Error

	if err != nil {
		return nil, err
	}

	stats := make(map[int32]int64)
	for _, result := range results {
		stats[result.DeviceLevel] = result.Count
	}

	return stats, nil
}

// GetRecentRecords 获取最近的设备分级记录
func (d *DeviceLevelRecordDAO) GetRecentRecords(ctx context.Context, limit int) ([]*model.DeviceLevelRecord, error) {
	var records []*model.DeviceLevelRecord
	err := d.db.WithContext(ctx).
		Order("update_time DESC").
		Limit(limit).
		Find(&records).Error

	return records, err
}

// DeleteByDeviceID 根据设备ID删除设备分级记录
func (d *DeviceLevelRecordDAO) DeleteByDeviceID(ctx context.Context, deviceID string) error {
	return d.db.WithContext(ctx).Where("h_did = ?", deviceID).Delete(&model.DeviceLevelRecord{}).Error
}

// CleanOldRecords 清理旧记录
func (d *DeviceLevelRecordDAO) CleanOldRecords(ctx context.Context, days int) error {
	cutoffTime := time.Now().AddDate(0, 0, -days)
	return d.db.WithContext(ctx).Where("update_time < ?", cutoffTime).Delete(&model.DeviceLevelRecord{}).Error
}

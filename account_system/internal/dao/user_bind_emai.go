package dao

import (
	"context"
	"github.com/sirupsen/logrus"
	"gorm.io/gorm"
	"new-gitlab.xunlei.cn/funup/account_system/internal/model"
)

type BindEmailDao struct {
	db *gorm.DB
}

func NewBindEmailDao(db *gorm.DB) *BindEmailDao {
	return &BindEmailDao{db: db}
}

func (b *BindEmailDao) Create(ctx context.Context, info model.BindEmail) error {
	if err := b.db.Create(&info).Error; err != nil {
		logrus.WithContext(ctx).Errorf("BindEmailDao Create error = %v", err)
		return err
	}
	return nil
}
func (b *BindEmailDao) UpdateById(ctx context.Context, info model.BindEmail) error {
	t := model.BindEmail{}
	if err := b.db.Table(t.TableName()).Where("id", info.ID).Updates(map[string]interface{}{
		"email":     info.Email,
		"bind_desc": info.BindDesc,
	}).Error; err != nil {
		logrus.WithContext(ctx).<PERSON><PERSON><PERSON>("UpdateById Create error = %v", err)
		return err
	}
	return nil
}

func (b *BindEmailDao) FirstByMid(ctx context.Context, mid int64) (*model.BindEmail, error) {
	var info model.BindEmail
	if err := b.db.Where("mid = ?", mid).First(&info).Error; err != nil {
		logrus.WithContext(ctx).Errorf("BindEmailDao FirstByMid error = %v", err)
		return nil, err
	}
	return &info, nil
}

func (b *BindEmailDao) FirstByEmail(ctx context.Context, email string) (*model.BindEmail, error) {
	var info model.BindEmail
	if err := b.db.Where("email = ?", email).
		First(&info).Error; err != nil {
		logrus.WithContext(ctx).Errorf("BindEmailDao FirstByMid error = %v", err)
		return nil, err
	}
	return &info, nil
}
func (b *BindEmailDao) FindBindRecords(ctx context.Context, mid int64) ([]*model.BindEmailRecord, error) {
	var info []*model.BindEmailRecord
	if err := b.db.Where("mid = ?", mid).Order("created_at desc").
		Find(&info).Error; err != nil {
		logrus.WithContext(ctx).Errorf("FindBindRecords  error = %v", err)
		return nil, err
	}
	return info, nil
}

func (b *BindEmailDao) DeleteByMid(ctx context.Context, mid int64, email string) error {
	if err := b.db.Where("mid = ? AND email = ?",
		mid, email).Delete(&model.BindEmail{}).Error; err != nil {
		logrus.WithContext(ctx).Errorf("BindEmailDao DeleteByMid error = %v", err)
		return err
	}
	return nil
}

func (d *BindEmailDao) Update(ctx context.Context,
	info model.BindEmail) error {
	if err := d.db.Table(info.TableName()).Where("mid = ? and email = ?",
		info.Mid, info.Email).UpdateColumns(map[string]interface{}{
		"salt":          info.Salt,
		"hash_password": info.HashPassword,
	}).Error; err != nil {
		logrus.WithContext(ctx).Errorf("BindEmailDao Update error = %v", err)
		return err
	}
	return nil
}

func (b *BindEmailDao) SaveRecord(ctx context.Context, info model.BindEmailRecord) error {
	if err := b.db.Create(&info).Error; err != nil {
		logrus.WithContext(ctx).Errorf("SaveRecord Create error = %v", err)
		return err
	}
	return nil
}

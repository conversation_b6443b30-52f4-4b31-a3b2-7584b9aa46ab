package dao

import (
	"context"
	"github.com/sirupsen/logrus"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo"
	"new-gitlab.xunlei.cn/funup/account_system/internal/model"
)

type Member struct {
	mongoClient *mongo.Client
}

func NewMemberDao(mongoClient *mongo.Client) *Member {
	return &Member{mongoClient: mongoClient}
}

func (m *Member) FindOne(ctx context.Context, mid int64) (*model.User, error) {
	log := logrus.WithContext(ctx).WithFields(logrus.Fields{
		"func": "FindOne",
		"mid":  mid,
	})
	var user model.User
	database := m.mongoClient.Database(`account`)
	err := database.Collection(`member`).FindOne(ctx, bson.M{
		"_id": mid,
	}).Decode(&user)
	if err != nil {
		log.Errorf("error = %v", err)
		return nil, err
	}
	return &user, nil
}

func (m *Member) UpdateOne(ctx context.Context, mid int64, gender int64) error {
	log := logrus.WithContext(ctx).WithFields(logrus.Fields{
		"func":   "UpdateOne",
		"mid":    mid,
		"gender": gender,
	})
	database := m.mongoClient.Database(`account`)
	collection := database.Collection(`member`)
	// 执行更新操作
	filter := bson.M{"_id": mid}
	update := bson.M{"$set": bson.M{"gender": gender}}
	_, err := collection.UpdateOne(context.TODO(), filter, update)
	if err != nil {
		log.Errorf("error = %v", err)
		return err
	}
	return nil
}

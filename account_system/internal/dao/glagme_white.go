package dao

import (
	"fmt"
	"time"

	"github.com/sirupsen/logrus"
	"gorm.io/gorm"
	"new-gitlab.xunlei.cn/funup/account_system/internal/model"
)

// 批量增加白名单

type GlgameWhiteDao struct {
	db *gorm.DB
}

func NewGlgameWhiteDao(db *gorm.DB) *GlgameWhiteDao {
	return &GlgameWhiteDao{db: db}
}

// BatchCreate 批量增加
func (d *GlgameWhiteDao) BatchCreate(list []*model.GlgameWhite) (err error) {
	err = d.db.Model(&model.GlgameWhite{}).CreateInBatches(list, 1000).Error
	if err != nil {
		logrus.Errorf("批量插入数据失败 err:%v", err)
		return
	}
	return
}

// BatchUpdate 批量删除
func (d *GlgameWhiteDao) BatchUpdate(midList []int64, state model.GlgameWhiteState, operator string) (err error) {
	if len(midList) <= 0 {
		return
	}
	err = d.db.Model(&model.GlgameWhite{}).Where("mid in ?", midList).Updates(map[string]interface{}{
		"state":      state,
		"operator":   operator,
		"updated_at": time.Now(),
	}).Error
	if err != nil {
		logrus.Errorf("批量插入数据失败 err:%v", err)
		return
	}
	return
}

func (d *GlgameWhiteDao) BatchGetGlgameWhiteList(mids []int64) ([]*model.GlgameWhite, error) {
	if d.db == nil {
		return nil, fmt.Errorf("数据库连接未初始化")
	}

	var records []*model.GlgameWhite
	// 使用 Where 方法根据 mids 切片批量查询数据
	result := d.db.Model(&model.GlgameWhite{}).Where("mid IN ? and state = ?", mids, model.GlgameWhiteStateNormal).Find(&records)
	if result.Error != nil {
		logrus.Errorf("批量查询白名单数据失败: %v", result.Error)
		return nil, result.Error
	}

	return records, nil
}

func (d *GlgameWhiteDao) BatchGetGlgameWhiteListByPage(mids []int64, page, pageSize int64) (list []*model.GlgameWhite, total int64, err error) {
	// 使用 Where 方法根据 mids 切片批量查询数据
	sql := d.db.Model(&model.GlgameWhite{}).Where(" state = ?", model.GlgameWhiteStateNormal)
	if len(mids) > 0 {
		sql.Where("mid in ? ", mids)
	}
	err = sql.Count(&total).Error
	if err != nil {
		logrus.Errorf("批量查询白名单数据失败: %v", err)
		return
	}
	offset := 0
	if page > 0 {
		offset = int((page - 1) * pageSize)
	}

	err = sql.Offset(offset).Limit(int(pageSize)).Order("id desc").Find(&list).Error
	if err != nil {
		logrus.Errorf("批量查询白名单数据失败: %v", err)
		return
	}

	return
}

func (d *GlgameWhiteDao) BatchSetGlgameWhite(mids []int64, glGame string) error {
	// 准备要插入的数据
	var records []model.GlgameWhite
	for _, mid := range mids {
		record := model.GlgameWhite{
			Mid:    mid,
			GlGame: glGame,
			State:  1,
			// 若结构体还有其他字段，可按需补充
		}
		records = append(records, record)
	}

	// 批量插入数据
	if d.db == nil {
		// 可根据实际情况添加错误处理逻辑，这里仅做示例打印
		return error(fmt.Errorf("数据库连接未初始化"))
	}
	result := d.db.Create(&records)
	if result.Error != nil {
		// 可根据实际情况添加错误处理逻辑，这里仅做示例打印
		logrus.Errorf("批量插入数据失败:", result.Error.Error())
		return result.Error
	}
	return nil
}

func (d *GlgameWhiteDao) BatchGetGlgameWhite(mids []int64) ([]model.GlgameWhite, error) {
	if d.db == nil {
		return nil, fmt.Errorf("数据库连接未初始化")
	}

	var records []model.GlgameWhite
	// 使用 Where 方法根据 mids 切片批量查询数据
	result := d.db.Where("mid IN ?", mids).Find(&records)
	if result.Error != nil {
		logrus.Errorf("批量查询白名单数据失败: %v", result.Error)
		return nil, result.Error
	}

	return records, nil
}

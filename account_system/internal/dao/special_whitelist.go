package dao

import (
	"context"
	"github.com/sirupsen/logrus"
	"gorm.io/gorm"
	"new-gitlab.xunlei.cn/funup/account_system/internal/model"
)

type SpecialWhitelistDao struct {
	db *gorm.DB
}

func NewSpecialWhitelistDao(db *gorm.DB) *SpecialWhitelistDao {
	return &SpecialWhitelistDao{db: db}
}

// Exists 判断是否存在mid
func (s *SpecialWhitelistDao) Exists(ctx context.Context, mid int64) (bool, error) {
	var special model.SpecialWhitelist
	err := s.db.Where(`mid = ?`, mid).First(&special).Error
	if err == gorm.ErrRecordNotFound {
		return false, nil
	}
	if err != nil {
		logrus.WithContext(ctx).Errorf("SpecialWhitelistDao Exists error = %v", err)
		return false, err
	}
	return special.Mid == mid, nil
}

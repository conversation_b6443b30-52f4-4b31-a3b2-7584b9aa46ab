package dao

import (
	"context"
	"time"

	"new-gitlab.xunlei.cn/funup/account_system/internal/model"

	"gorm.io/gorm"
)

// DynamicLevelRecordDAO 动态分级记录DAO
type DynamicLevelRecordDAO struct {
	db *gorm.DB
}

// NewDynamicLevelRecordDAO 创建动态分级记录DAO实例
func NewDynamicLevelRecordDAO(db *gorm.DB) *DynamicLevelRecordDAO {
	return &DynamicLevelRecordDAO{db: db}
}

// SaveOrUpdate 保存或更新动态分级记录
func (d *DynamicLevelRecordDAO) SaveOrUpdate(ctx context.Context, record *model.DynamicLevelRecord) error {
	if record == nil {
		return nil
	}

	// 设置更新时间
	record.UpdateTime = time.Now()

	// 使用原生SQL实现ON DUPLICATE KEY UPDATE
	sql := `
		INSERT INTO cn_me_account.dynamic_level_record (
			h_did, h_dt, h_os, h_model, h_appid, h_av, h_m, h_ch, h_carrier, h_area_region,
			room_id, scene, dt_type, net_type, net_latency, net_throughput, quota_scores,
			is_relegate, score, net_speed_level, update_time, create_time
		) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
		ON DUPLICATE KEY UPDATE
			h_dt = VALUES(h_dt),
			h_os = VALUES(h_os),
			h_model = VALUES(h_model),
			h_appid = VALUES(h_appid),
			h_av = VALUES(h_av),
			h_m = VALUES(h_m),
			h_ch = VALUES(h_ch),
			h_carrier = VALUES(h_carrier),
			h_area_region = VALUES(h_area_region),
			room_id = VALUES(room_id),
			dt_type = VALUES(dt_type),
			net_type = VALUES(net_type),
			net_latency = VALUES(net_latency),
			net_throughput = VALUES(net_throughput),
			quota_scores = VALUES(quota_scores),
			is_relegate = VALUES(is_relegate),
			score = VALUES(score),
			net_speed_level = VALUES(net_speed_level),
			update_time = VALUES(update_time)
	`

	// 如果是新记录，设置创建时间
	if record.CreateTime.IsZero() {
		record.CreateTime = time.Now()
	}

	return d.db.WithContext(ctx).Exec(sql,
		record.HDid, record.HDt, record.HOs, record.HModel, record.HAppid, record.HAv,
		record.HM, record.HCh, record.HCarrier, record.HAreaRegion,
		record.RoomId, record.Scene, record.DtType, record.NetType, record.NetLatency,
		record.NetThroughput, record.QuotaScores, record.IsRelegate, record.Score,
		record.NetSpeedLevel, record.UpdateTime, record.CreateTime,
	).Error
}

// GetByDeviceID 根据设备ID获取最新的动态分级记录
func (d *DynamicLevelRecordDAO) GetByDeviceID(ctx context.Context, deviceID string) (*model.DynamicLevelRecord, error) {
	var record model.DynamicLevelRecord
	err := d.db.WithContext(ctx).
		Where("h_did = ?", deviceID).
		Order("update_time DESC").
		First(&record).Error
	if err != nil {
		return nil, err
	}
	return &record, nil
}

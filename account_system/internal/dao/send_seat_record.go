package dao

import (
	"context"
	"github.com/sirupsen/logrus"
	"gorm.io/gorm"
	"new-gitlab.xunlei.cn/funup/account_system/internal/model"
)

type SendSeatRecordDao struct {
	db *gorm.DB
}

func NewSendSeatRecordDao(db *gorm.DB) *SendSeatRecordDao {
	return &SendSeatRecordDao{db: db}
}

// Exists 判断是否存在mid
func (s *SendSeatRecordDao) Exists(ctx context.Context, mid int64) (bool, error) {
	var special model.SendSeatRecord
	err := s.db.Where(`mid = ?`, mid).First(&special).Error
	if err == gorm.ErrRecordNotFound {
		return false, nil
	}
	if err != nil {
		logrus.WithContext(ctx).Errorf("SendSeatRecordDao Exists error = %v", err)
		return false, err
	}
	return special.Mid == mid, nil
}

func (b *SendSeatRecordDao) Create(ctx context.Context, info model.SendSeatRecord) error {
	if err := b.db.Create(&info).Error; err != nil {
		logrus.WithContext(ctx).Errorf("SendSeatRecordDao Create error = %v", err)
		return err
	}
	return nil
}

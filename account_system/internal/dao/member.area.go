package dao

import (
	"context"
	"github.com/sirupsen/logrus"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo"
	"new-gitlab.xunlei.cn/funup/account_system/internal/model"
)

type MemberArea struct {
	mongoClient *mongo.Client
}

func NewMemberAreaDao(mongoClient *mongo.Client) *MemberArea {
	return &MemberArea{mongoClient: mongoClient}
}

func (m *MemberArea) FindOne(ctx context.Context, mid int64) (*model.MemberArea, error) {
	log := logrus.WithContext(ctx).WithFields(logrus.Fields{
		"func": "MemberArea.FindOne",
		"mid":  mid,
	})
	var user model.MemberArea
	database := m.mongoClient.Database(`account`)
	err := database.Collection(`member_area`).FindOne(ctx, bson.M{
		"_id": mid,
	}).Decode(&user)
	if err != nil {
		log.Errorf("error = %v", err)
		return nil, err
	}
	return &user, nil
}

package dao

import (
	"context"
	"github.com/sirupsen/logrus"
	"gorm.io/gorm"
	"new-gitlab.xunlei.cn/funup/account_system/internal/model"
	"time"
)

type AnchorTaskDao struct {
	db *gorm.DB
}

func NewAnchorTaskDao(db *gorm.DB) *AnchorTaskDao {
	return &AnchorTaskDao{db: db}
}

// FindTaskListByMid 查询未开始和已经开始任务
func (a *AnchorTaskDao) FindTaskListByMid(ctx context.Context) ([]model.TaskList, error) {
	var taskList []model.TaskList
	nowTime := time.Now()
	if err := a.db.Where("audit_status = ? AND end_time > ?", 2, nowTime.Unix()).
		Find(&taskList).Error; err != nil {
		logrus.WithContext(ctx).Errorf("FindTaskListByMid error = %v", err)
		return nil, err
	}
	return taskList, nil
}

package dao

import (
	"context"
	"github.com/sirupsen/logrus"
	"gorm.io/gorm"
	"new-gitlab.xunlei.cn/funup/account_system/internal/model"
)

type ReviewAccountDao struct {
	db *gorm.DB
}

func NewReviewAccountDao(db *gorm.DB) *ReviewAccountDao {
	return &ReviewAccountDao{db: db}
}

func (r *ReviewAccountDao) FindAuditMid(ctx context.Context) ([]model.ReviewAccount, error) {
	var reviewAccount []model.ReviewAccount
	err := r.db.Find(&reviewAccount).Error
	if err != nil {
		logrus.WithContext(ctx).Errorf("FindAuditMid error = %v", err)
		return nil, err
	}
	return reviewAccount, nil
}

package dao

import (
	"context"
	"github.com/sirupsen/logrus"
	"gorm.io/gorm"
	"new-gitlab.xunlei.cn/funup/account_system/internal/model"
)

type DeviceRegisterDao struct {
	db *gorm.DB
}

func NewDeviceRegisterDao(db *gorm.DB) *DeviceRegisterDao {
	return &DeviceRegisterDao{db: db}
}

func (d *DeviceRegisterDao) Create(ctx context.Context,
	info model.DeviceRegisterInfo) error {
	if err := d.db.Create(&info).Error; err != nil {
		logrus.WithContext(ctx).Errorf("DeviceRegisterDao Create error = %v", err)
		return err
	}
	return nil
}

func (d *DeviceRegisterDao) Update(ctx context.Context,
	info model.DeviceRegisterInfo) error {
	if err := d.db.Table(info.TableName()).Where("mid = ? and h_did = ?",
		info.Mid, info.HDid).UpdateColumns(map[string]interface{}{
		"salt":          info.Salt,
		"hash_password": info.HashPassword,
	}).Error; err != nil {
		logrus.WithContext(ctx).Errorf("DeviceRegisterDao Update error = %v", err)
		return err
	}
	return nil
}

func (d *DeviceRegisterDao) FirstByDeviceID(ctx context.Context, deviceID string) (*model.DeviceRegisterInfo, error) {
	var info model.DeviceRegisterInfo
	err := d.db.Where("h_did = ?", deviceID).First(&info).Error
	if err != nil && err != gorm.ErrRecordNotFound {
		logrus.WithContext(ctx).Errorf("DeviceRegisterDao First error = %v", err)
		return nil, err
	}
	return &info, nil
}

func (d *DeviceRegisterDao) FirstByMid(ctx context.Context, mid int64) (*model.DeviceRegisterInfo, error) {
	var info model.DeviceRegisterInfo
	err := d.db.Where("mid = ?", mid).First(&info).Error
	if err != nil && err != gorm.ErrRecordNotFound {
		logrus.WithContext(ctx).Errorf("DeviceRegisterDao First error = %v", err)
		return nil, err
	}
	return &info, nil
}

func (d *DeviceRegisterDao) ExistByDeviceID(ctx context.Context, deviceID string) (*model.DeviceRegisterInfo, bool, error) {
	var info model.DeviceRegisterInfo
	err := d.db.Where("h_did = ?", deviceID).First(&info).Error
	if err != nil && err != gorm.ErrRecordNotFound {
		logrus.WithContext(ctx).Errorf("DeviceRegisterDao First error = %v", err)
		return nil, false, err
	}
	if info.HDid == deviceID {
		return &info, true, err
	}
	return &info, false, nil
}

func (d *DeviceRegisterDao) ExistByMid(ctx context.Context, mid int64) (*model.DeviceRegisterInfo, bool, error) {
	var info model.DeviceRegisterInfo
	err := d.db.Where("mid = ?", mid).First(&info).Error
	if err != nil && err != gorm.ErrRecordNotFound {
		logrus.WithContext(ctx).Errorf("DeviceRegisterDao First error = %v", err)
		return nil, false, err
	}
	if info.Mid == mid {
		return &info, true, err
	}
	return &info, false, nil
}

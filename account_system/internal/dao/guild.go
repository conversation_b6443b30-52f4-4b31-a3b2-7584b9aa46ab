package dao

import (
	"context"
	"github.com/sirupsen/logrus"
	"gorm.io/gorm"
	"new-gitlab.xunlei.cn/funup/account_system/internal/model"
)

type GuildDao struct {
	db *gorm.DB
}

func NewGuildDao(db *gorm.DB) *GuildDao {
	return &GuildDao{db: db}
}

func (g *GuildDao) FindGuildMember(ctx context.Context) ([]model.GuildMember, error) {
	var members []model.GuildMember
	if err := g.db.Where("role > ?", 0).Find(&members).Error; err != nil {
		logrus.WithContext(ctx).Errorf("FindGuildMember error = %v", err)
		return nil, err
	}
	return members, nil
}

// 剔除公会成员
func (g *GuildDao) DelGuildMemberRole(ctx context.Context, mid int64) error {
	if err := g.db.Where("mid = ?", mid).Delete(&model.GuildMember{}).Error; err != nil {
		logrus.WithContext(ctx).Errorf("DelGuildMemberRole error = %v", err)
		return err
	}
	return nil
}

// 删除加入公会日志
func (g *GuildDao) DelJoinGuildLog(ctx context.Context, mid int64) error {
	if err := g.db.Where("mid = ?", mid).Delete(&model.JoinGuildLog{}).Error; err != nil {
		logrus.WithContext(ctx).Errorf("DelJoinGuildLog error = %v", err)
		return err
	}
	return nil
}

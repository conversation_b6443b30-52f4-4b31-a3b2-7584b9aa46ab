package dao

import (
	"context"
	"errors"
	"fmt"
	"time"

	"github.com/sirupsen/logrus"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo"
	"gorm.io/gorm"
	"new-gitlab.xunlei.cn/funup/account_system/internal/model"
	accountSdk "new-gitlab.xunlei.cn/funup/base_sdk/account"
)

type MemberExt struct {
	mongoClient *mongo.Client
}

func NewMemberExtDao(mongoClient *mongo.Client) *MemberExt {
	return &MemberExt{mongoClient: mongoClient}
}

func (m *MemberExt) FindOne(ctx context.Context, mid int64) (*model.MemberExt, error) {
	log := logrus.WithContext(ctx).WithFields(logrus.Fields{
		"func": "FindOne",
		"mid":  mid,
	})
	var memberExt model.MemberExt
	database := m.mongoClient.Database(`account`)
	err := database.Collection(`member_ext`).FindOne(ctx, bson.M{
		"_id": mid,
	}).Decode(&memberExt)
	if err != nil {
		log.Errorf("error = %v", err)
		return nil, err
	}
	return &memberExt, nil
}

func (m *MemberExt) FindOneByALDid(ctx context.Context, did string) (*model.MemberExtV2, error) {
	log := logrus.WithContext(ctx).WithFields(logrus.Fields{
		"func": "FindOneByDid",
		"did":  did,
	})
	var memberExt model.MemberExtV2
	database := m.mongoClient.Database(`account`)
	err := database.Collection(`member_ext`).FindOne(ctx, bson.M{
		"account_label_did": did,
	}).Decode(&memberExt)
	if errors.Is(err, mongo.ErrNoDocuments) {
		return &model.MemberExtV2{}, nil
	}
	if err != nil {
		log.Errorf("error = %v", err)
		return nil, err
	}
	return &memberExt, nil
}

func (m *MemberExt) FindOneV2(ctx context.Context, mid int64) (*model.MemberExtV2, error) {
	log := logrus.WithContext(ctx).WithFields(logrus.Fields{
		"func": "FindOneV2",
		"mid":  mid,
	})
	var memberExt model.MemberExtV2
	database := m.mongoClient.Database(`account`)
	err := database.Collection(`member_ext`).FindOne(ctx, bson.M{
		"_id": mid,
	}).Decode(&memberExt)
	if err != nil {
		log.Errorf("error = %v", err)
		return nil, err
	}

	if memberExt.ID > 0 {
		return &memberExt, nil
	} else {
		return &model.MemberExtV2{}, nil
	}
}

func (m *MemberExt) UpdateOne(ctx context.Context, mid int64, area string) error {
	log := logrus.WithContext(ctx).WithFields(logrus.Fields{
		"func": "UpdateOne",
		"mid":  mid,
		"area": area,
	})
	database := m.mongoClient.Database(`account`)
	collection := database.Collection(`member_ext`)
	// 执行更新操作
	filter := bson.M{"_id": mid}
	update := bson.M{"$set": bson.M{"area_region": area}}
	_, err := collection.UpdateOne(context.TODO(), filter, update)
	if err != nil {
		log.Errorf("error = %v", err)
		return err
	}
	return nil
}

func (m *MemberExt) FindMany(ctx context.Context, mids []int64) (map[int64]*model.MemberExt, error) {
	log := logrus.WithContext(ctx).WithFields(logrus.Fields{
		"func": "FindMany",
		"mids": mids,
	})
	memberExts := make(map[int64]*model.MemberExt)
	database := m.mongoClient.Database(`account`)
	cursor, err := database.Collection(`member_ext`).Find(ctx, bson.M{
		"_id": bson.M{"$in": mids},
	})
	if err != nil {
		log.Errorf("error = %v", err)
		return nil, err
	}
	defer func(cursor *mongo.Cursor, ctx context.Context) {
		err := cursor.Close(ctx)
		if err != nil {
			log.Errorf("cursor error = %v", err)
		}
	}(cursor, ctx)
	for cursor.Next(ctx) {
		var memberExt model.MemberExt
		if err := cursor.Decode(&memberExt); err != nil {
			log.Errorf("error = %v", err)
			continue // decide how to handle partial errors
		}
		memberExts[memberExt.ID] = &memberExt // assuming ID is the field name in memberExt struct that corresponds to _id
	}
	if err := cursor.Err(); err != nil {
		log.Errorf("error = %v", err)
		return nil, err
	}
	return memberExts, nil
}

func (m *MemberExt) UpdateCountryOne(ctx context.Context, mid int64, country string) error {
	log := logrus.WithContext(ctx).WithFields(logrus.Fields{
		"func":    "UpdateCountryOne",
		"mid":     mid,
		"country": country,
	})
	database := m.mongoClient.Database(`account`)
	collection := database.Collection(`member_area`)
	// 执行更新操作
	filter := bson.M{"_id": mid}
	update := bson.M{"$set": bson.M{"country": country, "is_mainland": false}}
	_, err := collection.UpdateOne(context.TODO(), filter, update)
	if err != nil {
		log.Errorf("error = %v", err)
		return err
	}
	return nil
}

type UserChangeDao struct {
	db         *gorm.DB
	relationDB *gorm.DB
}

func NewUserChangeDao(db *gorm.DB, relationDB *gorm.DB) *UserChangeDao {
	return &UserChangeDao{db: db, relationDB: relationDB}
}

func (a *UserChangeDao) UserAreaListCreate(data model.UserAreaList) (err error) {
	err = a.db.Create(&data).Error
	if err != nil {
		logrus.WithFields(logrus.Fields{
			"func": "UserChangeDao_UserAreaListCreate",
			"err":  err,
		}).Error()
		return
	}
	return
}

func (a *UserChangeDao) UserAreaListQuery(mid int64) (datas []model.UserAreaList, err error) {
	res := a.db.Table((&model.UserAreaList{}).TableName()).Where("mid = ?", mid).Order("id desc").Scan(&datas)
	if res.Error != nil {
		if res.Error == gorm.ErrRecordNotFound {
			return datas, nil
		}
		logrus.WithFields(logrus.Fields{
			"func": "UserChangeDao_UserAreaListQuery",
			"err":  res.Error,
		}).Error()
		return datas, res.Error
	}
	return
}

func (a *UserChangeDao) UserCountryListCreate(data model.UserCountryList) (err error) {
	err = a.db.Create(&data).Error
	if err != nil {
		logrus.WithFields(logrus.Fields{
			"func": "UserChangeDao_UserCountryListCreate",
			"err":  err,
		}).Error()
		return
	}
	return
}

func (a *UserChangeDao) UserCountryListQuery(mid int64) (datas []model.UserCountryList, err error) {
	res := a.db.Table((&model.UserCountryList{}).TableName()).Where("mid = ?", mid).Order("id desc").Scan(&datas)
	if res.Error != nil {
		if res.Error == gorm.ErrRecordNotFound {
			return datas, nil
		}
		logrus.WithFields(logrus.Fields{
			"func": "UserChangeDao_UserCountryListQuery",
			"err":  res.Error,
		}).Error()
		return datas, res.Error
	}
	return
}

func (a *UserChangeDao) UpdateUserCoin(mid int64, userArea string) (err error) {
	pointRate := accountSdk.GetCoinRateByArea(userArea)
	if pointRate == 0 {
		err = errors.New("point rate is zero")
		return
	}

	tx := a.db.Begin()

	// 用户金币、钻石
	var midUserTradeInfo model.UserTradeInfo
	res := tx.Table((&model.UserTradeInfo{}).TableName()).Where("id = ?", mid).Set("gorm:query_option", "FOR UPDATE").First(&midUserTradeInfo)
	if res.Error != nil && res.Error != gorm.ErrRecordNotFound {
		err = res.Error
		tx.Rollback()
		return
	}
	// 需要先加明细后更新余额，核心用于对账（保证明细的创建时间小于等于余额的更新时间）
	if midUserTradeInfo.CoinPoints > 0 || midUserTradeInfo.TicketPoints > 0 {

		// 计算被抹除的数值
		coinPointsLost := int64(midUserTradeInfo.CoinPoints) % pointRate
		ticketPointsLost := int64(midUserTradeInfo.TicketPoints) % pointRate

		userTradeDetailMonthTable := fmt.Sprintf("me_trade_detail.user_trade_detail_%s", time.Now().Format("200601"))

		// 比例调整的记录
		now := time.Now()
		extId := now.Format("20060102_150405")
		if midUserTradeInfo.CoinPoints > 0 &&
			int64(midUserTradeInfo.CoinPoints)/pointRate-int64(midUserTradeInfo.Coins) != 0 {
			tx.Table((&model.UserTradeDetail{}).TableName()).Create(&model.UserTradeDetail{
				ID:           fmt.Sprintf("change_coins_%d_%s", midUserTradeInfo.Id, extId),
				Mid:          midUserTradeInfo.Id,
				Count:        int64(midUserTradeInfo.CoinPoints)/pointRate - int64(midUserTradeInfo.Coins),
				CurrencyType: "coins",
				// From:         "金币比例调整",
				From:       "Gold coin adjustment",
				AreaRegion: userArea,
				ExtId:      extId,
				ExtInfo:    "",
				SdkApp:     "",
				RoomID:     0,
				Ct:         now,
			})
			tx.Table(userTradeDetailMonthTable).Create(&model.UserTradeDetail{
				ID:           fmt.Sprintf("change_coins_%d_%s", midUserTradeInfo.Id, extId),
				Mid:          midUserTradeInfo.Id,
				Count:        int64(midUserTradeInfo.CoinPoints)/pointRate - int64(midUserTradeInfo.Coins),
				CurrencyType: "coins",
				// From:         "金币比例调整",
				From:       "Gold coin adjustment",
				AreaRegion: userArea,
				ExtId:      extId,
				ExtInfo:    "",
				SdkApp:     "",
				RoomID:     0,
				Ct:         now,
			})
		}
		if midUserTradeInfo.TicketPoints > 0 &&
			int64(midUserTradeInfo.TicketPoints)/pointRate-int64(midUserTradeInfo.Tickets) != 0 {
			tx.Table((&model.UserTradeDetail{}).TableName()).Create(&model.UserTradeDetail{
				ID:           fmt.Sprintf("change_tickets_%d_%s", midUserTradeInfo.Id, extId),
				Mid:          midUserTradeInfo.Id,
				Count:        int64(midUserTradeInfo.TicketPoints)/pointRate - int64(midUserTradeInfo.Tickets),
				CurrencyType: "tickets",
				// From:         "钻石比例调整",
				From:       "Diamond adjustment",
				AreaRegion: userArea,
				ExtId:      extId,
				ExtInfo:    "",
				SdkApp:     "",
				RoomID:     0,
				Ct:         now,
			})
			tx.Table(userTradeDetailMonthTable).Create(&model.UserTradeDetail{
				ID:           fmt.Sprintf("change_tickets_%d_%s", midUserTradeInfo.Id, extId),
				Mid:          midUserTradeInfo.Id,
				Count:        int64(midUserTradeInfo.TicketPoints)/pointRate - int64(midUserTradeInfo.Tickets),
				CurrencyType: "tickets",
				// From:         "钻石比例调整",
				From:       "Diamond adjustment",
				AreaRegion: userArea,
				ExtId:      extId,
				ExtInfo:    "",
				SdkApp:     "",
				RoomID:     0,
				Ct:         now,
			})
		}
		// 记录被抹除的小数值
		if coinPointsLost > 0 {
			tx.Table((&model.UserTradeDetail{}).TableName()).Create(&model.UserTradeDetail{
				ID:           fmt.Sprintf("lost_coins_%d_%s", midUserTradeInfo.Id, extId),
				Mid:          midUserTradeInfo.Id,
				Count:        0,               // 必须为0，不可外显
				Point:        -coinPointsLost, // 负数表示损失
				CurrencyType: "coins",
				From:         "CoinPointsLostDueToRounding",
				AreaRegion:   userArea,
				ExtId:        extId,
				ExtInfo:      fmt.Sprintf("Original: %d, Lost: %d", midUserTradeInfo.CoinPoints, coinPointsLost),
				SdkApp:       "funhy",
				RoomID:       0,
				Ct:           now,
			})
			tx.Table(userTradeDetailMonthTable).Create(&model.UserTradeDetail{
				ID:           fmt.Sprintf("lost_coins_%d_%s", midUserTradeInfo.Id, extId),
				Mid:          midUserTradeInfo.Id,
				Count:        0,               // 必须为0，不可外显
				Point:        -coinPointsLost, // 负数表示损失
				CurrencyType: "coins",
				From:         "CoinPointsLostDueToRounding",
				AreaRegion:   userArea,
				ExtId:        extId,
				ExtInfo:      fmt.Sprintf("Original: %d, Lost: %d", midUserTradeInfo.CoinPoints, coinPointsLost),
				SdkApp:       "funhy",
				RoomID:       0,
				Ct:           now,
			})
		}
		if ticketPointsLost > 0 {
			tx.Table((&model.UserTradeDetail{}).TableName()).Create(&model.UserTradeDetail{
				ID:           fmt.Sprintf("lost_tickets_%d_%s", midUserTradeInfo.Id, extId),
				Mid:          midUserTradeInfo.Id,
				Count:        0,                 // 必须为0，不可外显
				Point:        -ticketPointsLost, // 负数表示损失
				CurrencyType: "tickets",
				From:         "TicketPointsLostDueToRounding",
				AreaRegion:   userArea,
				ExtId:        extId,
				ExtInfo:      fmt.Sprintf("Original: %d, Lost: %d", midUserTradeInfo.TicketPoints, ticketPointsLost),
				SdkApp:       "funhy",
				RoomID:       0,
				Ct:           now,
			})
			tx.Table(userTradeDetailMonthTable).Create(&model.UserTradeDetail{
				ID:           fmt.Sprintf("lost_tickets_%d_%s", midUserTradeInfo.Id, extId),
				Mid:          midUserTradeInfo.Id,
				Count:        0,                 // 必须为0，不可外显
				Point:        -ticketPointsLost, // 负数表示损失
				CurrencyType: "tickets",
				From:         "TicketPointsLostDueToRounding",
				AreaRegion:   userArea,
				ExtId:        extId,
				ExtInfo:      fmt.Sprintf("Original: %d, Lost: %d", midUserTradeInfo.TicketPoints, ticketPointsLost),
				SdkApp:       "funhy",
				RoomID:       0,
				Ct:           now,
			})
		}

		res = tx.Table((&model.UserTradeInfo{}).TableName()).Where("id = ?", mid).
			UpdateColumns(map[string]interface{}{
				"coins":         int64(midUserTradeInfo.CoinPoints) / pointRate,
				"tickets":       int64(midUserTradeInfo.TicketPoints) / pointRate,
				"coin_points":   int64(midUserTradeInfo.CoinPoints) / pointRate * pointRate,
				"ticket_points": int64(midUserTradeInfo.TicketPoints) / pointRate * pointRate,
			})
		if res.Error != nil {
			err = res.Error
			tx.Rollback()
			return
		}
	}

	// 贵族币
	var midNobleTradeInfo model.NobleTradeInfo
	res = tx.Table((&model.NobleTradeInfo{}).TableName()).Where("mid = ?", mid).Set("gorm:query_option", "FOR UPDATE").First(&midNobleTradeInfo)
	if res.Error != nil && res.Error != gorm.ErrRecordNotFound {
		err = res.Error
		tx.Rollback()
		return
	}
	if midNobleTradeInfo.NobleCoinPoints > 0 {
		res = tx.Table((&model.NobleTradeInfo{}).TableName()).Where("mid = ?", mid).
			UpdateColumns(map[string]interface{}{
				"noble_coins":       midNobleTradeInfo.NobleCoinPoints / pointRate,
				"noble_coin_points": midNobleTradeInfo.NobleCoinPoints / pointRate * pointRate,
			})
		if res.Error != nil {
			err = res.Error
			tx.Rollback()
			return
		}
	}

	// 渠道币
	var midChannelTradeInfo model.ChannelTradeInfo
	res = tx.Table((&model.ChannelTradeInfo{}).TableName()).Where("mid = ?", mid).Set("gorm:query_option", "FOR UPDATE").First(&midChannelTradeInfo)
	if res.Error != nil && res.Error != gorm.ErrRecordNotFound {
		err = res.Error
		tx.Rollback()
		return
	}
	if midChannelTradeInfo.ChannelCoinPoints > 0 {
		res = tx.Table((&model.ChannelTradeInfo{}).TableName()).Where("mid = ?", mid).
			UpdateColumns(map[string]interface{}{
				"channel_coins":       midChannelTradeInfo.ChannelCoinPoints / pointRate,
				"channel_coin_points": midChannelTradeInfo.ChannelCoinPoints / pointRate * pointRate,
			})
		if res.Error != nil {
			err = res.Error
			tx.Rollback()
			return
		}
	}

	if err = tx.Commit().Error; err != nil {
		logrus.WithFields(logrus.Fields{
			"func": "UpdateUserCoin",
			"err":  err,
		}).Error()
		tx.Rollback()
		return
	}

	relationTx := a.relationDB.Begin()
	// 币商币
	var midUserAgentInfo model.Agent
	res = relationTx.Table((&model.Agent{}).TableName()).Where("mid = ?", mid).Set("gorm:query_option", "FOR UPDATE").First(&midUserAgentInfo)
	if res.Error != nil && res.Error != gorm.ErrRecordNotFound {
		err = res.Error
		relationTx.Rollback()
		return
	}
	if midUserAgentInfo.CurrentCoinPoints.IntPart() > 0 {
		res = relationTx.Table((&model.Agent{}).TableName()).Where("mid = ?", mid).
			UpdateColumns(map[string]interface{}{
				"current_coins":       midUserAgentInfo.CurrentCoinPoints.IntPart() / pointRate,
				"current_coin_points": midUserAgentInfo.CurrentCoinPoints.IntPart() / pointRate * pointRate,
			})
		if res.Error != nil {
			err = res.Error
			relationTx.Rollback()
			return
		}
	}
	if err = relationTx.Commit().Error; err != nil {
		logrus.WithFields(logrus.Fields{
			"func": "UpdateUserCoin",
			"err":  err,
		}).Error()
		relationTx.Rollback()
		return
	}

	return
}

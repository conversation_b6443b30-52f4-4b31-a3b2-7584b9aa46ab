package dao

import (
	"context"
	"github.com/sirupsen/logrus"
	"gorm.io/gorm"
	"new-gitlab.xunlei.cn/funup/account_system/internal/model"
)

type MccDao struct {
	db *gorm.DB
}

func NewMccDao(db *gorm.DB) *MccDao {
	return &MccDao{db: db}
}

func (m *MccDao) FindMccCountry(ctx context.Context) ([]model.MccCountry, error) {
	log := logrus.WithContext(ctx).WithFields(logrus.Fields{
		"func": "BelongArea",
	})
	var results []model.MccCountry
	if err := m.db.Find(&results).Error; err != nil {
		log.Errorf("error = %v", err)
		return nil, err
	}
	return results, nil
}

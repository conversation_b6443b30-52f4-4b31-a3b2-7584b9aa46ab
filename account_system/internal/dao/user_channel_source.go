package dao

import (
	"gorm.io/gorm"
	"new-gitlab.xunlei.cn/funup/account_system/internal/model"
)

type UserChannelSourceDao struct {
	db *gorm.DB
}

func NewUserChannelSourceDao(db *gorm.DB) *UserChannelSourceDao {
	return &UserChannelSourceDao{db: db}
}

// BatchGetByMids returns map[mid]channel for given mids
func (d *UserChannelSourceDao) BatchGetByMids(mids []int64) (map[int64]string, error) {
	res := make(map[int64]string, len(mids))
	if len(mids) == 0 {
		return res, nil
	}
	var rows []model.UserChannelSource
	if err := d.db.Table((model.UserChannelSource{}).TableName()).Where("mid in ?", mids).Find(&rows).Error; err != nil {
		return nil, err
	}
	for _, r := range rows {
		res[r.Mid] = r.Channel
	}
	return res, nil
}

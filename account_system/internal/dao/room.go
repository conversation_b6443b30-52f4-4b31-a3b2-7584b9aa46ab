package dao

import (
	"context"
	"github.com/sirupsen/logrus"
	"gorm.io/gorm"
	"new-gitlab.xunlei.cn/funup/account_system/internal/model"
)

type RoomDao struct {
	db *gorm.DB
}

func NewRoomDao(db *gorm.DB) *RoomDao {
	return &RoomDao{db: db}
}

func (r *RoomDao) UpdateUserRoomArea(ctx context.Context, mid int64, area string) error {
	return r.db.Table("me_room.user_room").Where("mid = ?", mid).Updates(map[string]interface{}{
		"area_region": area,
	}).Error
}

func (r *RoomDao) FindKindRoom(ctx context.Context) ([]model.KindRoom, error) {
	var rooms []model.KindRoom
	err := r.db.Where("status = ?", 1).Find(&rooms).Error
	if err != nil {
		logrus.WithContext(ctx).Errorf("FindKindRoom error = %v", err)
		return nil, err
	}
	return rooms, nil
}

type TotalTimeParams struct {
	Mid     int64
	Roles   []int32
	RoomIds []int64
}

func (r *RoomDao) FindMidOnMicTotalTimeParams(params TotalTimeParams) ([]*model.MidOnMicTime, error) {
	var ret []*model.MidOnMicTime
	query := r.db.Table("data_center.room_mid_mic_heartbeat")
	query = query.Select("sum(on_time) as total_on_time,mid")
	if params.Mid != 0 {
		query = query.Where("mid = ?", params.Mid)
	}
	if len(params.RoomIds) != 0 {
		query = query.Where("room_id in (?)", params.RoomIds)
	}
	if len(params.Roles) != 0 {
		query = query.Where("role in (?)", params.Roles)
	}
	err := query.Group(`mid`).Scan(&ret).Error
	if err != nil {
		logrus.Errorf("mic dao FindHostMicOnTime error = %v", err)
		return nil, err
	}
	return ret, nil
}

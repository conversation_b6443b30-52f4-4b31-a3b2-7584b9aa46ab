package dao

import (
	"context"
	"github.com/sirupsen/logrus"
	"gorm.io/gorm"
	"new-gitlab.xunlei.cn/funup/account_system/internal/model"
)

type ImpressionsDao struct {
	db *gorm.DB
}

func NewImpressionsDao(db *gorm.DB) *ImpressionsDao {
	return &ImpressionsDao{db: db}
}

// FindUserImpressionsId 查询用户标签信息
func (g *ImpressionsDao) FindUserImpressionsId(ctx context.Context, mids []int64) (map[int64]*model.UserImpressions, error) {
	var impressionsData []*model.UserImpressions
	userImpressionsMap := make(map[int64]*model.UserImpressions, 0)

	// 查询普通成员
	err := g.db.Where(" mid in (?)", mids).Find(&impressionsData).Error
	if err != nil {
		logrus.WithContext(ctx).Errorf("GuildDao FindGuildWithMember error = %v", err)
		return userImpressionsMap, err
	}
	for _, item := range impressionsData {
		userImpressionsMap[item.Mid] = item
	}
	return userImpressionsMap, nil
}

// FindAllImpressions 查询标签信息
func (g *ImpressionsDao) FindAllImpressions(ctx context.Context) (map[int64]*model.Impressions, error) {
	var impressionsData []*model.Impressions

	impressionsMap := make(map[int64]*model.Impressions, 0)
	// 查询普通成员
	err := g.db.Find(&impressionsData).Error
	if err != nil {
		logrus.WithContext(ctx).Errorf("GuildDao FindGuildWithMember error = %v", err)
		return impressionsMap, err
	}
	for _, item := range impressionsData {
		impressionsMap[item.ID] = item
	}
	return impressionsMap, nil
}

package dao

import (
	"github.com/pkg/errors"
	"gorm.io/gorm"
	"new-gitlab.xunlei.cn/funup/account_system/internal/model"
	"time"
)

type InviteCodeDao struct {
	db *gorm.DB
}

func NewInviteCodeDao(db *gorm.DB) *InviteCodeDao {
	return &InviteCodeDao{db: db}
}

// ExistsCode 判断是否存在code
func (i *InviteCodeDao) ExistsCode(code string) (
	dao *model.RegisterCode, exists bool, err error) {
	sql := i.db
	err = sql.Where(`code=? and expired_at>?`, code, time.Now().Unix()).First(&dao).Error
	if err == gorm.ErrRecordNotFound {
		return dao, false, nil
	}
	if err != nil {
		err = errors.Wrapf(err, "faield to find code")
	}
	return dao, true, nil
}

func NewInviteCodeRelationDao(db *gorm.DB) *InviteCodeDao {
	return &InviteCodeDao{db: db}
}

// RelationExistsCode 判断是否已经绑定邀请码
func (i *InviteCodeDao) RelationExistsCode(mid int64) (
	dao *model.RegisterCodeRelation, exists bool, err error) {
	sql := i.db
	err = sql.Where(`mid=? `, mid).First(&dao).Error
	if err == gorm.ErrRecordNotFound {
		return dao, false, nil
	}
	if err != nil {
		err = errors.Wrapf(err, "faield to find code")
	}
	return dao, true, nil
}

// DelRelationExistsCode 解绑当前用户的邀请码
func (i *InviteCodeDao) DelRelationExistsCode(mid int64) (err error) {
	sql := i.db
	err = sql.Where(`mid=? `, mid).Delete(&model.RegisterCodeRelation{}).Error
	if err == gorm.ErrRecordNotFound {
		return nil
	}
	if err != nil {
		err = errors.Wrapf(err, "faield to find code")
	}
	return nil
}

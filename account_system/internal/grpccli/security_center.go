package grpccli

import (
	"context"
	"fmt"
	middleware "github.com/grpc-ecosystem/go-grpc-middleware"
	retry "github.com/grpc-ecosystem/go-grpc-middleware/retry"
	prometheus "github.com/grpc-ecosystem/go-grpc-prometheus"
	"github.com/sirupsen/logrus"
	"google.golang.org/grpc"
	"google.golang.org/grpc/credentials/insecure"
	"new-gitlab.xunlei.cn/funup/yaplay_proto/security_center"
	"os"
	"time"
)

var (
	grpcUrl  string
	grpcConn *grpc.ClientConn
	client   security_center.AppServiceClient
)

func Init() {
	env := os.Getenv("stage")
	//env := "test"
	if env == `dev` {
		grpcUrl = fmt.Sprintf("consul://consul-prod.imfunup.com:8500/hiya_%s-security_center", env)
	} else if env == `test` {
		grpcUrl = fmt.Sprintf("consul://consul-prod.imfunup.com:8500/hiya_%s-security_center", env)
	} else {
		grpcUrl = fmt.Sprintf("consul://consul-prod.imfunup.com:8500/hiya_%s-security_center", env)
	}
	initGrpcConn(grpcUrl)
	grpcConn = GetGrpcConn()
	client = security_center.NewAppServiceClient(grpcConn)
}

func initGrpcConn(grpcUrl string) {
	var err error
	var ctx, cancelFunc = context.WithTimeout(context.Background(), 5*time.Second)
	defer cancelFunc()

	grpcConn, err = grpc.DialContext(ctx, grpcUrl,
		grpc.WithTransportCredentials(insecure.NewCredentials()),
		grpc.WithBlock(),
		grpc.WithDefaultServiceConfig(`{"loadBalancingPolicy": "round_robin"}`),
		grpc.WithUnaryInterceptor(middleware.ChainUnaryClient(
			prometheus.UnaryClientInterceptor,
			retry.UnaryClientInterceptor(
				retry.WithPerRetryTimeout(60*time.Second),
				retry.WithMax(3),
			),
		)),
	)
	if err != nil {
		logrus.Errorf(`initSecurityGrpcConn err. urlPrefix:%s`, grpcUrl)
		//logrus.WithError(err).WithField(`urlPrefix`, grpcUrl).Panic("did not connect.")
	}
}

func GetGrpcConn() *grpc.ClientConn {
	return grpcConn
}

func GetGrpcClient() security_center.AppServiceClient {
	return client
}

// DescribeOnlineUserIdentity 判断当前登录的用户的身份,0:普通用户，1：黑产，2：受保护人群
func DescribeOnlineUserIdentity(ctx context.Context, mid int64) int64 {
	logrus.WithContext(ctx).WithField("mid", mid).Printf("DescribeOnlineUserIdentity interface")
	securityClient := GetGrpcClient()
	if securityClient == nil {
		logrus.Errorf("grpc GetGrpcClient empty")
		return 0
	}
	req := &security_center.DescribeOnlineUserIdentityReq{
		HM: mid,
	}
	res, err := securityClient.DescribeOnlineUserIdentity(ctx, req)
	if err != nil || res == nil {
		logrus.Errorf("grpc DescribeOnlineUserIdentity err %v", res)
		return 0
	}

	logrus.Infof("DescribeOnlineUserIdentity res %v ", res)
	return res.Tag
}

// DescribeUserIdentityByTag 查询受保护人群，黑产人群mid列表
func DescribeUserIdentityByTag(ctx context.Context, tag int64) []int64 {
	logrus.WithContext(ctx).WithField("tag", tag).Printf("DescribeUserIdentityByTag interface")
	securityClient := GetGrpcClient()
	mids := make([]int64, 0)
	if securityClient == nil {
		logrus.Errorf("grpc GetGrpcClient empty")
		return mids
	}
	req := &security_center.DescribeUserIdentityByTagReq{
		Tag: tag,
	}
	res, err := securityClient.DescribeUserIdentityByTag(ctx, req)
	if err != nil || res == nil {
		logrus.Errorf("grpc DescribeUserIdentityByTag err %v", res)
		return mids
	}

	logrus.Infof("DescribeUserIdentityByTag res %v ", res)
	return res.Mid
}

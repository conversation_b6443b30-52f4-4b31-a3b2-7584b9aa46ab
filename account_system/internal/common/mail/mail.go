package mail

import (
	"crypto/tls"
	"fmt"
	"github.com/pkg/errors"
	"github.com/sirupsen/logrus"
	"gopkg.in/gomail.v2"
	"new-gitlab.xunlei.cn/funup/i18n_sdk"
)

// Client is the client of mail
type Client struct {
	username string
	password string
	host     string
	port     int
}

var mailClient = NewMail(
	"smtpdm-ap-southeast-1.aliyun.com",
	80,
	"<EMAIL>",
	"FeAU4mfn1SOu",
)

func NewMail(host string, port int, username string, password string) *Client {
	return &Client{
		username: username,
		password: password,
		host:     host,
		port:     port,
	}
}

// SendCaptcha send captcha to mail
func (client *Client) SendCaptcha(toMail string, nickname string, captcha, language string) error {
	d := gomail.NewDialer(client.host, client.port, client.username, client.password)
	d.TLSConfig = &tls.Config{InsecureSkipVerify: true}

	var msgSetting = gomail.SetEncoding(gomail.QuotedPrintable)
	m := gomail.NewMessage(msgSetting)
	// 发件人
	send := i18n_sdk.GetMultiCopyWritingV2("account_system_mail_43", language, "FunUp官方", nil)
	m.SetHeader("From", m.FormatAddress(client.username, send))
	// 收件人
	m.SetHeader("To", toMail)
	// 主体
	subject := i18n_sdk.GetMultiCopyWritingV2("account_system_mail_48", language, "【FunUp】驗證您的電子郵件", nil)
	m.SetHeader("Subject", subject)
	if nickname != "" {
		nickname = fmt.Sprintf("【%s】", nickname)
	}

	text := i18n_sdk.GetMultiCopyWritingV2("account_system_mail_54", language, `您好，您的電子郵件地址%v將添加至您的%vFunUp賬號。</br>
要完成郵箱驗證，請將下面的驗證碼填入app郵箱綁定頁。</br></br>

驗證碼：%v</br></br>

（該驗證碼只能使用5分鐘，請盡快使用。過期後可在app內重新發送）</br></br>

如果%v不是您的FunUp賬號，請通過FunUp APP在線客服聯繫我們。`, nil)
	// 正文
	m.SetBody("text/html", fmt.Sprintf(text, toMail, nickname, captcha, nickname))

	err := d.DialAndSend(m)
	if err != nil {
		logrus.Errorf("DialAndSend fail. err:%s, m:%+v", err, m)
		return errors.Wrap(err, "send mail failed")
	}
	return nil
}

// SendCaptchaForResetPwd send captcha to mail
func (client *Client) SendCaptchaForResetPwd(toMail string, nickname string, captcha, language string) error {
	d := gomail.NewDialer(client.host, client.port, client.username, client.password)
	d.TLSConfig = &tls.Config{InsecureSkipVerify: true}

	var msgSetting = gomail.SetEncoding(gomail.QuotedPrintable)
	m := gomail.NewMessage(msgSetting)
	// 发件人
	send := i18n_sdk.GetMultiCopyWritingV2("account_system_mail_43", language, "FunUp官方", nil)
	m.SetHeader("From", m.FormatAddress(client.username, send))
	// 收件人
	m.SetHeader("To", toMail)
	// 主体
	subject := i18n_sdk.GetMultiCopyWritingV2("account_system_mail_48", language, "【FunUp】驗證您的電子郵件", nil)
	m.SetHeader("Subject", subject)
	if nickname != "" {
		nickname = fmt.Sprintf("【%s】", nickname)
	}

	// 正文
	text := i18n_sdk.GetMultiCopyWritingV2("account_system_mail_92", language, `您好，您正在通過您的電子郵件地址%s，<b>重置</b>您的%sFunUp賬號<b>密碼</b>。要完成郵箱驗證，請將下面的驗證碼填入app密碼重置頁。</br></br>

<strong>驗證碼：%s</strong></br></br>

（該驗證碼只能使用5分鐘，請盡快使用。過期後可在app內重新發送）</br></br>

如果%s不是您的FunUp賬號，請通過FunUp APP在線客服聯繫我們。`, nil)

	m.SetBody("text/html", fmt.Sprintf(text, toMail, nickname, captcha, nickname))

	err := d.DialAndSend(m)
	if err != nil {
		return errors.Wrap(err, "send mail failed")
	}
	return nil
}

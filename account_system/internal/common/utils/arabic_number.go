package utils

import (
	"strings"
)

// ArabicToLatinDigits 将阿拉伯语数字转换为拉丁数字
// 阿拉伯语数字: ٠١٢٣٤٥٦٧٨٩
// 拉丁数字: 0123456789
func ArabicToLatinDigits(input string) string {
	arabicDigits := map[rune]rune{
		'٠': '0',
		'١': '1',
		'٢': '2',
		'٣': '3',
		'٤': '4',
		'٥': '5',
		'٦': '6',
		'٧': '7',
		'٨': '8',
		'٩': '9',
	}

	// 阿拉伯语小数点
	arabicDecimalPoint := '٫'
	latinDecimalPoint := '.'

	result := strings.Builder{}
	for _, char := range input {
		if digit, exists := arabicDigits[char]; exists {
			result.WriteRune(digit)
		} else if char == arabicDecimalPoint {
			result.WriteRune(latinDecimalPoint)
		} else {
			result.WriteRune(char)
		}
	}

	return result.String()
}

// IsArabicNumber 检查字符串是否包含阿拉伯语数字
func IsArabicNumber(input string) bool {
	arabicDigits := []rune{'٠', '١', '٢', '٣', '٤', '٥', '٦', '٧', '٨', '٩', '٫'}

	for _, char := range input {
		for _, arabicDigit := range arabicDigits {
			if char == arabicDigit {
				return true
			}
		}
	}
	return false
}

// NormalizeNumberString 标准化数字字符串，处理阿拉伯语数字和其他格式问题
func NormalizeNumberString(input string) string {
	if input == "" {
		return input
	}

	// 如果包含阿拉伯语数字，先转换
	if IsArabicNumber(input) {
		input = ArabicToLatinDigits(input)
	}

	// 移除前后空格
	input = strings.TrimSpace(input)

	// 处理可能的千位分隔符（如逗号）
	// 但保留小数点
	parts := strings.Split(input, ".")
	if len(parts) > 0 {
		// 移除整数部分的千位分隔符
		parts[0] = strings.ReplaceAll(parts[0], ",", "")
	}

	if len(parts) > 1 {
		// 重新组合，保留小数点
		return strings.Join(parts, ".")
	}

	// 检查是否使用逗号作为小数点分隔符（如 2,77 3,57）
	// 如果字符串包含逗号且逗号后面是数字，则认为是小数点分隔符
	if strings.Contains(input, ",") {
		commaParts := strings.Split(input, ",")
		if len(commaParts) == 2 {
			// 检查逗号前后是否都是数字
			beforeComma := strings.TrimSpace(commaParts[0])
			afterComma := strings.TrimSpace(commaParts[1])

			// 简单的数字检查：只包含数字
			isBeforeNumeric := true
			isAfterNumeric := true
			for _, char := range beforeComma {
				if char < '0' || char > '9' {
					isBeforeNumeric = false
					break
				}
			}
			for _, char := range afterComma {
				if char < '0' || char > '9' {
					isAfterNumeric = false
					break
				}
			}

			if isBeforeNumeric && isAfterNumeric {
				// 将逗号替换为小数点
				return beforeComma + "." + afterComma
			}
		}
	}

	return parts[0]
}

package utils

import (
	"testing"
)

func TestArabicToLatinDigits(t *testing.T) {
	tests := []struct {
		input    string
		expected string
	}{
		{"٣٫٦٢", "3.62"},
		{"٣٫٧٢", "3.72"},
		{"٣٫٧٧", "3.77"},
		{"١٢٣", "123"},
		{"٠١٢٣٤٥٦٧٨٩", "0123456789"},
		{"٣٫٦٢٠", "3.620"},
		{"١٠٠٠", "1000"},
		{"normal text", "normal text"},
		{"", ""},
	}

	for _, test := range tests {
		result := ArabicToLatinDigits(test.input)
		if result != test.expected {
			t.Errorf("ArabicToLatinDigits(%q) = %q, expected %q", test.input, result, test.expected)
		}
	}
}

func TestIsArabicNumber(t *testing.T) {
	tests := []struct {
		input    string
		expected bool
	}{
		{"٣٫٦٢", true},
		{"٣٫٧٢", true},
		{"٣٫٧٧", true},
		{"١٢٣", true},
		{"٠١٢٣٤٥٦٧٨٩", true},
		{"٣٫٦٢٠", true},
		{"normal text", false},
		{"123", false},
		{"3.62", false},
		{"", false},
	}

	for _, test := range tests {
		result := IsArabicNumber(test.input)
		if result != test.expected {
			t.Errorf("IsArabicNumber(%q) = %v, expected %v", test.input, result, test.expected)
		}
	}
}

func TestNormalizeNumberString(t *testing.T) {
	tests := []struct {
		input    string
		expected string
	}{
		{"٣٫٦٢", "3.62"},
		{"٣٫٧٢", "3.72"},
		{"٣٫٧٧", "3.77"},
		{"١٢٣", "123"},
		{"٣٫٦٢٠", "3.620"},
		{"1,234.56", "1234.56"},
		{" ٣٫٦٢ ", "3.62"},
		{"2,77", "2.77"},
		{"3,57", "3.57"},
		{"10,5", "10.5"},
		{"0,123", "0.123"},
		{"normal text", "normal text"},
		{"", ""},
	}

	for _, test := range tests {
		result := NormalizeNumberString(test.input)
		if result != test.expected {
			t.Errorf("NormalizeNumberString(%q) = %q, expected %q", test.input, result, test.expected)
		}
	}
}

func TestArabicAndCommaNumbers(t *testing.T) {
	// 测试阿拉伯语数字
	arabicTests := []struct {
		input    string
		expected string
	}{
		{"٣٫٦٢", "3.62"},
		{"٣٫٧٢", "3.72"},
		{"٣٫٧٧", "3.77"},
	}

	for _, test := range arabicTests {
		result := NormalizeNumberString(test.input)
		if result != test.expected {
			t.Errorf("Arabic number: NormalizeNumberString(%q) = %q, expected %q", test.input, result, test.expected)
		}
	}

	// 测试逗号分隔符
	commaTests := []struct {
		input    string
		expected string
	}{
		{"2,77", "2.77"},
		{"3,57", "3.57"},
		{"10,5", "10.5"},
		{"0,123", "0.123"},
	}

	for _, test := range commaTests {
		result := NormalizeNumberString(test.input)
		if result != test.expected {
			t.Errorf("Comma separator: NormalizeNumberString(%q) = %q, expected %q", test.input, result, test.expected)
		}
	}

	// 测试普通数字（不应该被修改）
	normalTests := []struct {
		input    string
		expected string
	}{
		{"3.62", "3.62"},
		{"3.72", "3.72"},
		{"123", "123"},
	}

	for _, test := range normalTests {
		result := NormalizeNumberString(test.input)
		if result != test.expected {
			t.Errorf("Normal number: NormalizeNumberString(%q) = %q, expected %q", test.input, result, test.expected)
		}
	}
}

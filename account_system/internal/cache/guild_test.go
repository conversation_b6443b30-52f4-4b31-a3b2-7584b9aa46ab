package cache

import (
	"github.com/stretchr/testify/assert"
	"testing"
	"time"
)

func TestGuildCache_GetGuildInfoByMid(t *testing.T) {
	InitGuild("https://test-api.imfunup.com/hiya_guild", time.Minute)
	useCase := NewGuildCacheUseCase()

	guildInfo := useCase.GetGuildInfoByMid(5)
	assert.NotNil(t, guildInfo)
	assert.Equal(t, guildInfo.Joined, true)
	assert.Equal(t, guildInfo.Info.Id, 100316)

	guildInfo = useCase.GetGuildInfoByMid(5)
	assert.NotNil(t, guildInfo)
	assert.Equal(t, guildInfo.Joined, true)
	assert.Equal(t, guildInfo.Info.Id, 100316)

	guildInfo = useCase.GetGuildInfoByMid(99999999)
	assert.NotNil(t, guildInfo)
	assert.Equal(t, guildInfo.Joined, false)

	guildInfo = useCase.GetGuildInfoByMid(99999999)
	assert.NotNil(t, guildInfo)
	assert.Equal(t, guildInfo.Joined, false)
}

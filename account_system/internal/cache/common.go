package cache

import (
	"context"
	"encoding/json"
	"github.com/go-redis/redis/v8"
	"github.com/sirupsen/logrus"
	"time"
)

type CommonCache struct {
	rc         *redis.Client
	defaultTTL time.Duration
}

func NewDefaultCommonCache(rc *redis.Client) *CommonCache {
	return &CommonCache{rc: rc}
}

func (c *CommonCache) GetDataStr(ctx context.Context, key string) (str string, err error) {
	data, err := c.rc.Get(ctx, key).Bytes()
	if err != nil {
		return
	}

	if len(data) == 0 {
		return
	}

	str = string(data)
	return
}

func (c *CommonCache) ExistsKey(ctx context.Context, key string) (ok bool, err error) {
	data, err := c.rc.Exists(ctx, key).Result()
	if err != nil {
		return
	}

	if data > 0 {
		ok = true
	}

	return
}

func (c *CommonCache) GetData(ctx context.Context, key string, output interface{}) (ok bool, err error) {
	data, err := c.rc.Get(ctx, key).Bytes()
	if err != nil {
		return
	}

	if len(data) == 0 {
		return
	}

	err = json.Unmarshal(data, &output)
	if err != nil {
		c.DelKey(ctx, key)
		return
	}

	ok = true
	return
}

func (c *CommonCache) GetDataTTL(ctx context.Context, key string) (ttl time.Duration, err error) {
	ttl, err = c.rc.TTL(ctx, key).Result()
	if err != nil {
		return
	}

	return
}

func (c *CommonCache) SetKeyNx(ctx context.Context, key string, value interface{}, expiration time.Duration) (bool, error) {
	ok, err := c.rc.SetNX(ctx, key, value, expiration).Result()

	if err != nil {
		logrus.WithContext(ctx).Errorf("err %v", err)
		return false, err
	}

	return ok, nil
}

func (c *CommonCache) DelKey(ctx context.Context, key string) error {
	_, err := c.rc.Del(ctx, key).Result()

	if err != nil {
		logrus.WithContext(ctx).Errorf("err %v", err)
		return err
	}

	return nil
}

func (c *CommonCache) Expired(ctx context.Context, key string, ttl int64) (result bool, err error) {
	result, err = c.rc.Expire(ctx, key, time.Duration(ttl)*time.Second).Result() // 过期

	return
}

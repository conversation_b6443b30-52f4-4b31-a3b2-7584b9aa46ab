package cache

import (
	"context"
	"github.com/patrickmn/go-cache"
	"new-gitlab.xunlei.cn/funup/account_system/internal/proxy/guild"
	"strconv"
	"time"
)

type GuildCache struct {
	localCache *cache.Cache
	url        string
	defaultTTL time.Duration
}

var guildCacheUseCase *GuildCache

func InitGuild(url string, defaultTTL time.Duration) {
	guildCacheUseCase = &GuildCache{
		localCache: cache.New(time.Hour, 5*time.Second),
		url:        url,
		defaultTTL: time.Minute,
	}
}

func NewGuildCacheUseCase() *GuildCache {
	return guildCacheUseCase
}

func (c *GuildCache) set(key string, value *guild.GetGuildInfoByMidData, ttl time.Duration) {
	c.localCache.Set(key, value, ttl)
}

func (c *GuildCache) get(key string) (*guild.GetGuildInfoByMidData, bool) {
	get, b := c.localCache.Get(key)
	if b {
		return get.(*guild.GetGuildInfoByMidData), b
	}
	return nil, b
}

func (c *GuildCache) guildInfoLocalKey(mid int64) string {
	return "guildInfo_" + strconv.Itoa(int(mid))
}

// GetGuildInfoByMid get guild info by mid
func (c *GuildCache) GetGuildInfoByMid(mid int64) *guild.GetGuildInfoByMidData {
	key := c.guildInfoLocalKey(mid)
	value, exists := c.get(key)
	if exists {
		return value
	}

	apiClient := guild.NewApiClient(context.Background(), c.url)
	response, err := apiClient.GetGuildInfoByMid(mid)
	if err != nil {
		return &guild.GetGuildInfoByMidData{}
	}

	d := &response.Data
	c.set(key, d, c.defaultTTL)

	return d
}

package cache

import (
	"context"
	"github.com/patrickmn/go-cache"
	"github.com/sirupsen/logrus"
	"new-gitlab.xunlei.cn/funup/account_system/internal/config"
	"new-gitlab.xunlei.cn/funup/account_system/internal/proxy/account"
	"new-gitlab.xunlei.cn/funup/yaplay_proto/base_proto"
	"strconv"
	"strings"
	"time"
	"zlutils/yaplay/utils"
)

type MemberCache struct {
	localCache  *cache.Cache
	localKey    string
	localKeyLen int
	defaultTTL  time.Duration
}

var memberCacheUseCase *MemberCache = &MemberCache{
	localCache:  cache.New(time.Hour, 5*time.Second),
	localKey:    "memberInfo_",
	localKeyLen: len("memberInfo_"),
	defaultTTL:  time.Hour,
}

// NewMemberCache new member cache
func NewMemberCache() *MemberCache {
	return memberCacheUseCase
}

func (m *MemberCache) memberInfoLocalKey(mid int64) string {
	var buf strings.Builder
	midStr := strconv.Itoa(int(mid))
	buf.Grow(m.localKeyLen + len(midStr))
	buf.WriteString(m.localKey)
	buf.WriteString(midStr)

	return buf.String()
}

// GrpcGetMemberInfoByMidArr get member info by mid-array
func (m *MemberCache) GrpcGetMemberInfoByMidArr(midList []int64) map[int64]*base_proto.MemberInfo {
	if len(midList) == 0 {
		return map[int64]*base_proto.MemberInfo{}
	}

	midList = utils.FilterRepeatIds(midList)

	var missMid = make([]int64, 0, len(midList))

	var res = make(map[int64]*base_proto.MemberInfo, len(midList))

	for _, mid := range midList {
		if mid == 0 {
			continue
		}
		key := m.memberInfoLocalKey(mid)
		memberInfo, exists := m.localCache.Get(key)
		if !exists {
			missMid = append(missMid, mid)
			continue
		}
		res[mid] = memberInfo.(*base_proto.MemberInfo)
	}

	if len(missMid) == 0 {
		return res
	}

	newAccount := account.NewAccount(context.TODO(), config.RemoteApiConfig.AccountUrl)

	memberByIdsResp, err := newAccount.GetMemberByIdsV2(missMid)

	if err != nil || memberByIdsResp.Members == nil {
		logrus.WithError(err).WithFields(logrus.Fields{
			`resp`: memberByIdsResp, `midList`: missMid},
		).Error("MemberCache.GetMemberInfoByMidArr get member info by ids failed.")
		return res
	}

	for mid, st := range memberByIdsResp.Members {
		res[mid] = st
		key := m.memberInfoLocalKey(mid)
		m.localCache.Set(key, st, m.defaultTTL)
	}

	return res
}

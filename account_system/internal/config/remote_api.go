package config

import (
	"zlutils/consul"
)

// RemoteApiConfigWrapper 远程端口调用配置
type RemoteApiConfigWrapper struct {
	MsgDispatchUrl string `yaml:"msg_dispatch_url" validate:"required"`
	RoomUrl        string `yaml:"room_url" validate:"required"`
	AccountUrl     string `yaml:"account_url" validate:"required"`
	GiftUrl        string `yaml:"gift_url" validate:"required"`
	TradeUrl       string `yaml:"trade_url" validate:"required"`
	ChatCoreUrl    string `yaml:"chat_core_url" validate:"required"`
	ChatPushUrl    string `yaml:"chat_push_url" validate:"required"`
	RankUrl        string `yaml:"rank_url" validate:"required"`
	GuildUrl       string `yaml:"guild_url" validate:"required"`
	PrivilegeUrl   string `yaml:"privilege_url" validate:"required"`
	ClientLocation string `yaml:"client_location_url" validate:"required"`
	BuyUrl         string `yaml:"buy_url" validate:"required"`
}

// RemoteApiConfig 远程端口调用配置
var RemoteApiConfig *RemoteApiConfigWrapper

// checkRemoteApiConfig 检查远程端口调用配置
func checkRemoteApiConfig() {
	if RemoteApiConfig != nil {
		return
	}

	RemoteApiConfig = &RemoteApiConfigWrapper{}
	consul.ValiStruct().GetYaml("remote_api.yaml", RemoteApiConfig)

	//err := chatapi.Init(cfgstruct.ApiCfgSingleSt{
	//	AppHost: "http://hiya-chat-core:9745",
	//	AppName: "bizsrv-chat",
	//	NoSD:    true,
	//})
	//if err != nil {
	//	logrus.Panic("chat api.Init failed.")
	//}
}

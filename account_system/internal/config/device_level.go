package config

import (
	"zlutils/consul"

	"github.com/sirupsen/logrus"
)

// DeviceLevelConfig 设备分级配置
type DeviceLevelConfig struct {
	// iPhone低端设备基准型号（如：iPhone 12,1）
	IPhoneLowModel string `json:"iphone_low_model" yaml:"iphone_low_model"`
	// iPhone低端设备基准型号分数
	IPhoneLowModelScore int `json:"iphone_low_model_score" yaml:"iphone_low_model_score"`

	// Android低端设备型号列表
	AndroidLowLevelModelList []string `json:"android_low_level_model_list" yaml:"android_low_level_model_list"`

	// 内存容量评分列表
	MemoryCapacityList []DeviceLevelScoreDTO `json:"memory_capacity_list" yaml:"memory_capacity_list"`

	// 屏幕分辨率评分列表
	ScreenResolutionList []DeviceLevelScoreDTO `json:"screen_resolution_list" yaml:"screen_resolution_list"`

	// Android API级别评分列表
	AndroidApiLevelList []DeviceLevelScoreDTO `json:"android_api_level_list" yaml:"android_api_level_list"`

	// 物理设备特征映射表
	PhysicalDeviceMap map[string]int `json:"physical_device_map" yaml:"physical_device_map"`

	// 评分等级列表
	ScoreLevelList []DeviceScoreLevelDTO `json:"score_level_list" yaml:"score_level_list"`

	// 设备分级白名单 - 改为map类型，提高查找效率
	DeviceLevelWhiteMap map[string]bool `json:"device_level_white_map" yaml:"device_level_white_map"`

	// 设备分级黑名单 - 新增黑名单字段
	DeviceLevelBlackMap map[string]bool `json:"device_level_black_map" yaml:"device_level_black_map"`

	// 大区配置：map[大区ID]bool，命中配置则走评分逻辑，否则直接返回高设备
	AreaConfig map[string]bool `json:"area_config" yaml:"area_config"`

	// 端类型兜底分数配置
	NetTypeFallbackScoreMap map[string]int `json:"net_type_fallback_score_map" yaml:"net_type_fallback_score_map"`

	// 端类型兜底等级配置
	NetTypeFallbackLevelMap map[string]int `json:"net_type_fallback_level_map" yaml:"net_type_fallback_level_map"`

	// IPhone model 对应等级配置
	IPhoneModelLevelMap map[string]int `json:"iphone_model_level_map" yaml:"iphone_model_level_map"`
}

// DeviceLevelScoreDTO 设备评分DTO
type DeviceLevelScoreDTO struct {
	Min   float64 `json:"min" yaml:"min"`     // 最小值
	Max   float64 `json:"max" yaml:"max"`     // 最大值
	Score int     `json:"score" yaml:"score"` // 分数
}

// DeviceScoreLevelDTO 设备评分等级DTO
type DeviceScoreLevelDTO struct {
	Min   int `json:"min" yaml:"min"`     // 最小分数
	Max   int `json:"max" yaml:"max"`     // 最大分数
	Level int `json:"level" yaml:"level"` // 设备等级 1:低端设备 2:中端设备 3:高端设备
}

var (
	deviceLevelConfig *DeviceLevelConfig
)

func checkDeviceLevelConfig() {
	consul.WatchJsonVarious("device_level_config.json", func(d DeviceLevelConfig) {
		deviceLevelConfig = &d
		logrus.WithField("func", "checkDeviceLevelConfig").WithField("device_level_config", d).Info("device_level_config updated")
	})
}

func GetDeviceLevelConfig() *DeviceLevelConfig {
	return deviceLevelConfig
}

// // GetDeviceLevelConfig 获取设备分级配置单例
// func GetDeviceLevelConfig() *DeviceLevelConfig {
// 	deviceLevelOnce.Do(func() {
// 		deviceLevelConfig = &DeviceLevelConfig{
// 			IPhoneLowModel: "iPhone 12,1",
// 			AndroidLowLevelModelList: []string{
// 				"huaweip30", "huaweip20", "huaweimate20", "huaweimate10",
// 				"xiaomimi8", "xiaomimi6", "xiaomiredminote5", "xiaomiredminote4",
// 				"oppor15", "oppor11", "oppoa5", "oppoa3",
// 				"vivoiplay", "vivoy85", "vivoy75", "vivoy55",
// 				"samsunggalaxya50", "samsunggalaxya30", "samsunggalaxya20",
// 			},
// 			MemoryCapacityList: []DeviceLevelScoreDTO{
// 				{Min: 0, Max: 3, Score: 5},     // ≤ 3 GB
// 				{Min: 3, Max: 6, Score: 10},    // 3 - 6 GB
// 				{Min: 6, Max: 8, Score: 15},    // 6 - 8 GB
// 				{Min: 8, Max: 12, Score: 20},   // 8 - 12 GB
// 				{Min: 12, Max: 999, Score: 25}, // > 12 GB
// 			},
// 			ScreenResolutionList: []DeviceLevelScoreDTO{
// 				{Min: 0, Max: 720, Score: 0},      // 低于 HD+
// 				{Min: 720, Max: 1080, Score: 15},  // HD+
// 				{Min: 1080, Max: 9999, Score: 20}, // FHD+ 及以上
// 			},
// 			AndroidApiLevelList: []DeviceLevelScoreDTO{
// 				{Min: 0, Max: 28, Score: 0},    // Android < 9
// 				{Min: 28, Max: 32, Score: 5},   // Android 9-12
// 				{Min: 32, Max: 999, Score: 10}, // Android > 12
// 			},
// 			PhysicalDeviceMap: map[string]int{
// 				"1": 15, // 非模拟器
// 				"2": 0,  // 模拟器
// 			},
// 			ScoreLevelList: []DeviceScoreLevelDTO{
// 				{Min: 0, Max: 49, Level: 1},   // <50分：低端设备
// 				{Min: 50, Max: 79, Level: 2},  // 50-79分：中端设备
// 				{Min: 80, Max: 999, Level: 3}, // ≥80分：高端设备
// 			},
// 			DeviceLevelWhitelist: "", // 白名单设备ID，逗号分隔
// 		}
// 	})
// 	return deviceLevelConfig
// }

// SetDeviceLevelConfig 设置设备分级配置（用于测试）
func SetDeviceLevelConfig(config *DeviceLevelConfig) {
	deviceLevelConfig = config
}

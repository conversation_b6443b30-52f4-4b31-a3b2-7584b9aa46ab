package config

import "zlutils/consul"

// 登录哪些方式可用
type LoginType struct {
	Kinds []LoginKind `json:"kinds"`
}
type LoginKind struct {
	Enable bool   `json:"enable"`
	Rank   int    `json:"rank"`
	Name   string `json:"name"`
}

type LoginConf struct {
	IsOpenMainlandPhoneLogin bool          `json:"is_open_mainland_phone_login,omitempty"` //是否开启86手机登录入口
	IsOpenMidLogin           bool          `json:"is_open_mid_login,omitempty"`            //是否开启Id登录入口
	IsPopTips                bool          `json:"is_pop_tips,omitempty"`                  //是否启用弹窗提醒
	IsOpenEmailLogin         bool          `json:"is_open_email_login,omitempty"`          //是否启用邮箱提醒
	RegisterNumsLimit        int           `json:"register_nums_limit,omitempty"`          //注册数限制
	EmailRegisterLimit       int           `json:"email_register_limit,omitempty"`         //30天内注册数限制（仅统计邮箱数量）
	IsOpenOverseasEmailReg   bool          `json:"is_open_overseas_email_reg,omitempty"`   //是否开发海外邮箱注册
	LoginType                *LoginType    `json:"login_type,omitempty"`                   //登录类型
	LoginDidLimit            LoginDidLimit `json:"login_did_limit,omitempty"`              //登录限制
}
type LoginDidLimit struct {
	Days  int64 `json:"days"`
	Limit int64 `json:"limit"`
}

var LoginConfData *LoginConf

// checkLoginConfig
func checkLoginConfig() {
	if LoginConfData != nil {
		return
	}
	LoginConfData = &LoginConf{}
	consul.WatchJsonVarious(`login.json`, LoginConfData)
}

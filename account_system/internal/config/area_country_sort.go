package config

import "zlutils/consul"

type CountrySort struct {
	Country      string   `json:"country"`
	AreaSortList []string `json:"area_sort_list"`
}

type AreaCountrySort struct {
	Countries []CountrySort `json:"countries"`
}

var DefaultAreaCountrySortConfig *AreaCountrySort

// checkAreaCountrySortConf init area country sort
func checkAreaCountrySortConf() {
	if DefaultAreaCountrySortConfig != nil {
		return
	}
	consul.WatchJsonVarious(`area_country_sort.json`, &DefaultAreaCountrySortConfig)
}

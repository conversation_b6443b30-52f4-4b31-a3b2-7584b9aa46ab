package config

import (
	"zlutils/consul"
)

type AreaRedisConfigFormat struct {
	Addr     string `yaml:"addr" validate:"required"`
	Password string `yaml:"password"`
	Db       int    `yaml:"db"`
	PoolSize int    `yaml:"pool_size"`
}

var areaRedisConfig *AreaRedisConfigFormat

func GetAreaRedisConfig() *AreaRedisConfigFormat {
	return areaRedisConfig
}

func checkAreaRedisConfig() {
	if areaRedisConfig != nil {
		return
	}
	areaRedisConfig = &AreaRedisConfigFormat{}
	consul.ValiStruct().GetJson("area_redis.json", areaRedisConfig)
}

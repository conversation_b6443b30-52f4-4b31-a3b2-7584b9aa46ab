package config

import (
	"zlutils/consul"
)

type RedisConfigFormat struct {
	Addr     string `yaml:"addr" validate:"required"`
	Password string `yaml:"password"`
	Db       int    `yaml:"db"`
	PoolSize int    `yaml:"pool_size"`
}

var redisConfig *RedisConfigFormat

func GetRedisConfig() *RedisConfigFormat {
	return redisConfig
}

func checkRedisConfig() {
	if redisConfig != nil {
		return
	}

	redisConfig = &RedisConfigFormat{}
	consul.ValiStruct().GetJson("redis.json", redisConfig)
}

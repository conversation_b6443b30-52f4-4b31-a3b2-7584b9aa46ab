package config

type XcActionConfigFormat struct {
	Address  string `yaml:"address" validate:"required"`
	PoolSize int    `yaml:"pool_size" validate:"required"`
	Category string `yaml:"category" validate:"required"`
	Enable   bool   `yaml:"enable"`
}

// XcActionConfig 事件上报
var XcActionConfig = XcActionConfigFormat{
	Address:  "127.0.0.1:9121",
	PoolSize: 20,
	Category: "action",
	Enable:   true, // 开启事件上报
}

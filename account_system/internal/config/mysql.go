package config

import "zlutils/consul"

// MysqlConfig mysql config
type MysqlConfig struct {
	Url          string `json:"url"`
	SlaveUrl     string `json:"slave_url"`
	RelationUrl  string `json:"relation_url"`
	MaxOpenConns int    `json:"max_open_conns"`
	MaxIdleConns int    `json:"max_idle_conns"`
}

var DefaultMysqlConfig *MysqlConfig

// checkMysqlConf init mysql config
func checkMysqlConf() {
	if DefaultMysqlConfig != nil {
		return
	}
	consul.GetJson(`mysql_conf.json`, &DefaultMysqlConfig)
}

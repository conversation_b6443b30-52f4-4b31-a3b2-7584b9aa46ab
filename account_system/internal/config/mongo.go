package config

import "zlutils/consul"

// MongoConfig mysql config
type MongoConfig struct {
	Url             string `json:"url"`
	MaxConnIdleTime int64  `json:"max_conn_idle_time"`
	MaxPoolSize     int64  `json:"max_pool_size"`
}

var DefaultMongoConfig *MongoConfig

// checkMysqlConf init mysql config
func checkMongoConf() {
	if DefaultMongoConfig != nil {
		return
	}
	consul.WatchJsonVarious(`mongo_conf.json`, &DefaultMongoConfig)
}

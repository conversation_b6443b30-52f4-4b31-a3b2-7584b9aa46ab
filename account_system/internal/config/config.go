package config

import (
	"github.com/sirupsen/logrus"
	"os"
	"zlutils/consul"
)

const GrpcPort = 31071
const ProjectName = "account_system" // 服务名
var (
	Env      string
	AuthHost string
)

// EnvProd 正式环境
const EnvProd = "prod"
const EnvDev = "dev"
const EnvTest = "test"

// IsDevOrTest 是否是开发或测试环境
func IsDevOrTest() bool {
	return Env == EnvDev || Env == EnvTest
}

// IsTest 是否是测试环境
func IsTest() bool {
	return Env == EnvTest
}

// IsDev 是否是开发环境
func IsDev() bool {
	return Env == EnvDev
}

// IsProd 是否是正式环境
func IsProd() bool {
	return Env == EnvProd
}

func Init() {
	env := os.Getenv("stage")
	Env = env

	if Env == "" {
		Env = "dev"
	}
	var ConsulAddress, ConsulPrefix string
	AuthHost = "hiya-account:9906"

	switch Env {
	case "test":
		Consul<PERSON><PERSON><PERSON>, ConsulPrefix = "consul-prod.imfunup.com:8500", "hiya/test/service/account_system/"
	case "prod":
		ConsulAddress, ConsulPrefix = "consul-prod.imfunup.com:8500", "hiya/prod/service/account_system/"
	case "pre":
		ConsulAddress, ConsulPrefix = "consul-prod.imfunup.com:8500", "hiya/prod/service/account_system/"
	case "dev":
		ConsulAddress, ConsulPrefix = "consul-prod.imfunup.com:8500", "hiya/dev/service/account_system/"
		AuthHost = "test-api.imfunup.com/test_inner"
	default:
		logrus.Panicf("unsupported env: %s", env)
		return
	}

	consul.Init(ConsulAddress, ConsulPrefix)
	checkAreaCountrySortConf()
	checkRemoteApiConfig()
	checkMysqlConf()
	checkRedisConfig()
	checkRedisDB1Config()
	checkMongoConf()
	checkBaseConfigFormat()
	checkLoginConfig()
	checkBaseAreaTab()
	checkAreaRedisConfig()
	InitAppstoreReviewConf()
	checkFixRecommend()
	loadBannerConfig()
	checkBanAppCountryCodeConf()
	initNewbieConfig()
	checkDeviceLevelConfig()
	checkDynamicLevelConfig()
	logrus.Infof("checkBaseConfigFormat = [%+v]", BaseConfigFormat)
}

package config

import (
	"github.com/sirupsen/logrus"
	"sync/atomic"
	"zlutils/consul"
)

// AreaTabConfig 基础配置
type AreaTabConfig struct {
	AreaTagList  []*AreaCountryTab `json:"area_tab"`
	AreaWhiteMid []int64           `json:"area_white_mid"`
}

// AreaCountryTab 基
type AreaCountryTab struct {
	AreaCode    string     `json:"area_code"`
	AreaTagList []*AreaTab `json:"area_tag_list"`
}

// AreaTab 基
type AreaTab struct {
	Type  string `json:"type"`
	Exist bool   `json:"exist"`
}

// FixRecommendUser 修复推荐bug
type FixRecommendUser struct {
	Type string  `json:"type"`
	Mids []int64 `json:"mids"`
}

// BaseAreaTab 基
var BaseAreaTab AreaTabConfig

var FixRecommendUserInfo FixRecommendUser
var FixRecommendMid map[int64]bool // key 【mid】 , value 【true】
// 并发读写问题，改为用atomic存储
//var BaseAreaTabMap map[string][]*AreaTab // key 【cn】 , value 【AreaCountryTab】
//var BaseAreaMidMap map[int64]bool        // key 【mid】 , value 【true】

var AreaTabConf atomic.Value // 存储大区开关map， key 【cn】 , value 【AreaCountryTab】

// GetAreaTabMap 获取大区开关map
func GetAreaTabMap() map[string][]*AreaTab {
	data := AreaTabConf.Load()
	if data == nil {
		logrus.Errorf("AreaTabConf not found")
		return nil
	}
	ts, ok := data.(map[string][]*AreaTab)
	if !ok {
		logrus.Errorf("AreaTabConf not match")
		return nil
	}
	return ts
}

var AreaMidConf atomic.Value // 存储大区开关map， key 【mid】 , value 【true】

// GetAreaWhiteMidMap 获取大区白名单map
func GetAreaWhiteMidMap() map[int64]bool {
	data := AreaMidConf.Load()
	if data == nil {
		logrus.Errorf("AreaMidConf not found")
		return nil
	}
	whiteMap, ok := data.(map[int64]bool)
	if !ok {
		logrus.Errorf("AreaMidConf not match")
		return nil
	}
	return whiteMap
}

// checkBaseAreaTab 基础配置
func checkBaseAreaTab() {
	consul.WatchJsonVarious("area_tab_config.json", func(d AreaTabConfig) {
		tempAreaTabMap := make(map[string][]*AreaTab)
		tempAreaMidMap := make(map[int64]bool)
		logrus.Infof("area_tab_config.json configuration changed:%+v", d)
		BaseAreaTab = d

		for _, index := range BaseAreaTab.AreaTagList {
			tempAreaTabMap[index.AreaCode] = index.AreaTagList
			logrus.Infof("DefaultSwitch.AreaTagList status roomid:%v", index)
		}
		AreaTabConf.Store(tempAreaTabMap)

		for _, index := range BaseAreaTab.AreaWhiteMid {
			tempAreaMidMap[index] = true
		}
		AreaMidConf.Store(tempAreaMidMap)
	})
}

// checkBaseAreaTab 基础配置
func checkFixRecommend() {
	consul.WatchJsonVarious("fix_recommend.json", func(d FixRecommendUser) {
		// 	&FixRecommendUserInfo
		FixRecommendMid = make(map[int64]bool)
		FixRecommendUserInfo = d
		for _, v := range FixRecommendUserInfo.Mids {
			FixRecommendMid[v] = true
		}
		logrus.Infof("checkFixRecommend FixRecommendUserInfo: %+v", FixRecommendUserInfo)
	},
	)

}

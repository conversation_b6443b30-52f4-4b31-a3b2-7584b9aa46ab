package config

import (
	"zlutils/consul"
)

// BaseConfig 基础配置
type BaseConfig struct {
	IsOpen             bool               `json:"is_open"`
	EfficientGear      int64              `json:"efficient_gear"`
	AreaJudgeOpen      bool               `json:"area_judge_open"`
	AreaWhiteList      []int64            `json:"area_white_list"`       // 换区白名单
	GuildAreaWhiteList []int64            `json:"guild_area_white_list"` // 换区白名单
	Area2WhiteMidMap   map[string][]int64 `json:"area_2_white_mid_map"`  // 落区默认白名单
	CountryList        []*CountryInfo     `json:"country_list"`
	CountryForTest     string             `json:"country_for_test"` // 方便测试-指定国家
}

// CountryInfo 国家信息
type CountryInfo struct {
	Name string `json:"name"` // 国家名称
	Code string `json:"code"` // 国家码
	Area string `json:"area"` // 所属区
}

func (b BaseConfig) GetCountryCodeByArea(area string) map[string]bool {
	res := make(map[string]bool)
	var cs []*CountryInfo
	for _, c := range b.CountryList {
		if c.Area == area {
			cs = append(cs, c)
		}
	}
	for _, c := range cs {
		res[c.Code] = true
	}
	return res
}

func (b BaseConfig) IsHitWhiteMid(mid int64) string {
	for k, midList := range b.Area2WhiteMidMap {
		for _, m := range midList {
			if m == mid {
				return k
			}
		}
	}
	return ""
}

// BaseConfigFormat 基础配置
var BaseConfigFormat *BaseConfig

// checkBaseConfigFormat 基础配置
func checkBaseConfigFormat() {
	if BaseConfigFormat != nil {
		return
	}

	BaseConfigFormat = &BaseConfig{}
	consul.WatchJsonVarious("base_config.json", &BaseConfigFormat)
}

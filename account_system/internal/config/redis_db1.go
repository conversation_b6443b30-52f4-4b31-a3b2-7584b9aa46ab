package config

import (
	"zlutils/consul"
)

type RedisDB1ConfigFormat struct {
	Addr     string `yaml:"addr" validate:"required"`
	Password string `yaml:"password"`
	Db       int    `yaml:"db"`
	PoolSize int    `yaml:"pool_size"`
}

var redisdb1Config *RedisDB1ConfigFormat

func GetRedisDB1Config() *RedisDB1ConfigFormat {
	return redisdb1Config
}

func checkRedisDB1Config() {
	if redisdb1Config != nil {
		return
	}
	redisdb1Config = &RedisDB1ConfigFormat{}
	consul.ValiStruct().GetJson("redis_db1.json", redisdb1Config)
}

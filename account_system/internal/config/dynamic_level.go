package config

import (
	"zlutils/consul"

	"github.com/sirupsen/logrus"
)

// DynamicLevelConfig 动态分级配置
type DynamicLevelConfig struct {
	// 场景配置：map[端类型]map[场景]配置项
	SceneConfig map[string]map[string]*QuotaItem `json:"scene_config" yaml:"scene_config"`

	// 指标配置：map[指标ID]指标元数据
	QuotaConfig map[string]*QuotaMeta `json:"quota_config" yaml:"quota_config"`

	// 大区配置：map[大区ID]bool，命中配置则走评分逻辑，否则直接返回不降级
	AreaConfig map[string]bool `json:"area_config" yaml:"area_config"`

	// 根据场景的配置 key为场景
	SceneBlackWhiteConfigMap map[string]*SceneBlackWhiteConfig `json:"scene_black_white_config_map" yaml:"scene_black_white_config_map"`
}

type SceneBlackWhiteConfig struct {
	// 用户白名单：map[用户MID]bool，命中配置则返回不降级
	UserWhiteMap map[string]bool `json:"user_white_map" yaml:"user_white_map"`

	// 用户黑名单：map[用户MID]bool，命中配置则返回降级
	UserBlackMap map[string]bool `json:"user_black_map" yaml:"user_black_map"`

	// 房间白名单：map[房间ID]bool，命中配置则返回不降级
	RoomWhiteMap map[string]bool `json:"room_white_map" yaml:"room_white_map"`

	// 房间黑名单：map[房间ID]bool，命中配置则返回降级
	RoomBlackMap map[string]bool `json:"room_black_map" yaml:"room_black_map"`
}

// QuotaItem 场景配置项
type QuotaItem struct {
	// 指标列表
	QuotaList []string `json:"quota_list" yaml:"quota_list"`
	// 降级阈值
	RelegateThreshold int `json:"relegate_threshold" yaml:"relegate_threshold"`
	// 网速等级阈值配置
	NetSpeedThresholds *NetSpeedThresholds `json:"net_speed_thresholds" yaml:"net_speed_thresholds"`
}

// NetSpeedThresholds 网速等级阈值配置
type NetSpeedThresholds struct {
	// 极速阈值
	UltraFast int `json:"ultra_fast" yaml:"ultra_fast"`
	// 高速阈值
	HighSpeed int `json:"high_speed" yaml:"high_speed"`
	// 中速阈值
	MediumSpeed int `json:"medium_speed" yaml:"medium_speed"`
	// 低速阈值
	LowSpeed int `json:"low_speed" yaml:"low_speed"`
	// 极慢阈值
	UltraSlow int `json:"ultra_slow" yaml:"ultra_slow"`
}

// QuotaMeta 指标元数据
type QuotaMeta struct {
	// 指标名称
	Name string `json:"name" yaml:"name"`
	// 指标描述
	Desc string `json:"desc" yaml:"desc"`
	// 指标类型：net_delay、net_bandwidth、device_score、net_type
	Type QuotaType `json:"type" yaml:"type"`
	// 评分区间列表（用于net_delay、net_bandwidth、device_score）
	ScoreRanges []ScoreRange `json:"score_ranges" yaml:"score_ranges"`
	// 映射表（用于net_type）
	ScoreMap map[string]int `json:"score_map" yaml:"score_map"`
}

// QuotaType 指标类型
type QuotaType string

const (
	QuotaTypeNetDelay     QuotaType = "net_delay"     // 网络延迟指标
	QuotaTypeNetBandwidth QuotaType = "net_bandwidth" // 网络带宽指标
	QuotaTypeDeviceScore  QuotaType = "device_score"  // 设备分数指标
	QuotaTypeNetType      QuotaType = "net_type"      // 网络类型指标
)

// ScoreRange 评分区间
type ScoreRange struct {
	Min   float64 `json:"min" yaml:"min"`     // 最小值
	Max   float64 `json:"max" yaml:"max"`     // 最大值
	Score int     `json:"score" yaml:"score"` // 分数
}

var (
	dynamicLevelConfig *DynamicLevelConfig
)

func checkDynamicLevelConfig() {
	consul.WatchJsonVarious("dynamic_level_config.json", func(d DynamicLevelConfig) {
		dynamicLevelConfig = &d
		logrus.WithField("func", "checkDynamicLevelConfig").WithField("dynamic_level_config", d).Info("dynamic_level_config updated")
	})
}

func GetDynamicLevelConfig() *DynamicLevelConfig {
	return dynamicLevelConfig
}

// // GetDynamicLevelConfig 获取动态分级配置单例
// func GetDynamicLevelConfig() *DynamicLevelConfig {
// 	dynamicLevelMutex.Lock()
// 	defer dynamicLevelMutex.Unlock()

// 	// 如果已经设置了配置（测试模式），直接返回
// 	if dynamicLevelConfig != nil {
// 		return dynamicLevelConfig
// 	}

// 	// 否则使用默认配置
// 	dynamicLevelOnce.Do(func() {
// 		dynamicLevelConfig = &DynamicLevelConfig{
// 			// 场景配置示例
// 			SceneConfig: map[string]map[string]*QuotaItem{
// 				"1": { // 安卓端
// 					"rtc": {
// 						QuotaList:         []string{"net_delay_android_rtc", "net_bandwidth_android", "device_score"},
// 						RelegateThreshold: 100,
// 						NetSpeedThresholds: &NetSpeedThresholds{
// 							UltraFast:   1000,
// 							HighSpeed:   500,
// 							MediumSpeed: 200,
// 							LowSpeed:    100,
// 							UltraSlow:   50,
// 						},
// 					},
// 					"pic": {
// 						QuotaList:         []string{"device_score_pic", "net_type_pic", "net_bandwidth_android_pic"},
// 						RelegateThreshold: 100,
// 						NetSpeedThresholds: &NetSpeedThresholds{
// 							UltraFast:   1000,
// 							HighSpeed:   500,
// 							MediumSpeed: 200,
// 							LowSpeed:    100,
// 							UltraSlow:   50,
// 						},
// 					},
// 				},
// 				"2": { // iOS端
// 					"rtc": {
// 						QuotaList:         []string{"net_delay_ios_rtc", "net_bandwidth_ios", "device_score", "net_type"},
// 						RelegateThreshold: 100,
// 						NetSpeedThresholds: &NetSpeedThresholds{
// 							UltraFast:   1000,
// 							HighSpeed:   500,
// 							MediumSpeed: 200,
// 							LowSpeed:    100,
// 							UltraSlow:   50,
// 						},
// 					},
// 					"pic": {
// 						QuotaList:         []string{"device_score_pic", "net_type_pic"},
// 						RelegateThreshold: 100,
// 						NetSpeedThresholds: &NetSpeedThresholds{
// 							UltraFast:   1000,
// 							HighSpeed:   500,
// 							MediumSpeed: 200,
// 							LowSpeed:    100,
// 							UltraSlow:   50,
// 						},
// 					},
// 				},
// 			},

// 			// 指标配置 - 支持同种类型配置多份
// 			QuotaConfig: map[string]*QuotaMeta{
// 				// 网络延迟指标 - 安卓端RTC场景 (40分)
// 				"net_delay_android_rtc": {
// 					Name: "安卓RTC网络延迟",
// 					Desc: "安卓端RTC场景网络延迟评分，单位ms",
// 					Type: QuotaTypeNetDelay,
// 					ScoreRanges: []ScoreRange{
// 						{Min: 0, Max: 20, Score: 40},    // 0-20ms: 40分
// 						{Min: 20, Max: 50, Score: 30},   // 20-50ms: 30分
// 						{Min: 50, Max: 100, Score: 20},  // 50-100ms: 20分
// 						{Min: 100, Max: 200, Score: 10}, // 100-200ms: 10分
// 						{Min: 200, Max: 500, Score: 5},  // 200-500ms: 5分
// 						{Min: 500, Max: 9999, Score: 0}, // 500ms+: 0分
// 					},
// 				},
// 				// 网络延迟指标 - iOS端RTC场景 (35分)
// 				"net_delay_ios_rtc": {
// 					Name: "iOS RTC网络延迟",
// 					Desc: "iOS端RTC场景网络延迟评分，单位ms",
// 					Type: QuotaTypeNetDelay,
// 					ScoreRanges: []ScoreRange{
// 						{Min: 0, Max: 15, Score: 35},    // iOS要求更严格：0-15ms: 35分
// 						{Min: 15, Max: 40, Score: 25},   // 15-40ms: 25分
// 						{Min: 40, Max: 80, Score: 15},   // 40-80ms: 15分
// 						{Min: 80, Max: 150, Score: 10},  // 80-150ms: 10分
// 						{Min: 150, Max: 300, Score: 5},  // 150-300ms: 5分
// 						{Min: 300, Max: 9999, Score: 0}, // 300ms+: 0分
// 					},
// 				},
// 				// 网络带宽指标 - 安卓端RTC场景 (35分)
// 				"net_bandwidth_android": {
// 					Name: "安卓网络带宽",
// 					Desc: "安卓端网络带宽评分，单位kbps",
// 					Type: QuotaTypeNetBandwidth,
// 					ScoreRanges: []ScoreRange{
// 						{Min: 0, Max: 200, Score: 5},       // 0-200kbps: 5分
// 						{Min: 200, Max: 500, Score: 10},    // 200-500kbps: 10分
// 						{Min: 500, Max: 1000, Score: 15},   // 500-1000kbps: 15分
// 						{Min: 1000, Max: 2000, Score: 20},  // 1000-2000kbps: 20分
// 						{Min: 2000, Max: 5000, Score: 25},  // 2000-5000kbps: 25分
// 						{Min: 5000, Max: 99999, Score: 35}, // 5000kbps+: 35分
// 					},
// 				},
// 				// 网络带宽指标 - iOS端RTC场景 (30分)
// 				"net_bandwidth_ios": {
// 					Name: "iOS网络带宽",
// 					Desc: "iOS端网络带宽评分，单位kbps",
// 					Type: QuotaTypeNetBandwidth,
// 					ScoreRanges: []ScoreRange{
// 						{Min: 0, Max: 300, Score: 5},       // iOS要求更高：0-300kbps: 5分
// 						{Min: 300, Max: 800, Score: 10},    // 300-800kbps: 10分
// 						{Min: 800, Max: 1500, Score: 15},   // 800-1500kbps: 15分
// 						{Min: 1500, Max: 3000, Score: 20},  // 1500-3000kbps: 20分
// 						{Min: 3000, Max: 8000, Score: 25},  // 3000-8000kbps: 25分
// 						{Min: 8000, Max: 99999, Score: 30}, // 8000kbps+: 30分
// 					},
// 				},
// 				// 网络带宽指标 - 安卓端图片场景 (20分)
// 				"net_bandwidth_android_pic": {
// 					Name: "安卓图片网络带宽",
// 					Desc: "安卓端图片场景网络带宽评分，单位kbps",
// 					Type: QuotaTypeNetBandwidth,
// 					ScoreRanges: []ScoreRange{
// 						{Min: 0, Max: 200, Score: 2},       // 0-200kbps: 2分
// 						{Min: 200, Max: 500, Score: 5},     // 200-500kbps: 5分
// 						{Min: 500, Max: 1000, Score: 8},    // 500-1000kbps: 8分
// 						{Min: 1000, Max: 2000, Score: 12},  // 1000-2000kbps: 12分
// 						{Min: 2000, Max: 5000, Score: 16},  // 2000-5000kbps: 16分
// 						{Min: 5000, Max: 99999, Score: 20}, // 5000kbps+: 20分
// 					},
// 				},
// 				// 设备分数指标 - RTC场景 (25分)
// 				"device_score": {
// 					Name: "设备分数",
// 					Desc: "设备分级分数评分",
// 					Type: QuotaTypeDeviceScore,
// 					ScoreRanges: []ScoreRange{
// 						{Min: 0, Max: 30, Score: 5},     // 0-30分: 5分
// 						{Min: 30, Max: 50, Score: 10},   // 30-50分: 10分
// 						{Min: 50, Max: 70, Score: 15},   // 50-70分: 15分
// 						{Min: 70, Max: 85, Score: 20},   // 70-85分: 20分
// 						{Min: 85, Max: 100, Score: 25},  // 85-100分: 25分
// 						{Min: 100, Max: 999, Score: 25}, // 100分+: 25分
// 					},
// 				},
// 				// 设备分数指标 - 图片场景 (50分)
// 				"device_score_pic": {
// 					Name: "图片设备分数",
// 					Desc: "图片场景设备分级分数评分",
// 					Type: QuotaTypeDeviceScore,
// 					ScoreRanges: []ScoreRange{
// 						{Min: 0, Max: 30, Score: 10},    // 0-30分: 10分
// 						{Min: 30, Max: 50, Score: 20},   // 30-50分: 20分
// 						{Min: 50, Max: 70, Score: 30},   // 50-70分: 30分
// 						{Min: 70, Max: 85, Score: 40},   // 70-85分: 40分
// 						{Min: 85, Max: 100, Score: 50},  // 85-100分: 50分
// 						{Min: 100, Max: 999, Score: 50}, // 100分+: 50分
// 					},
// 				},
// 				// 网络类型指标 - RTC场景 (10分)
// 				"net_type": {
// 					Name: "网络类型",
// 					Desc: "网络类型评分",
// 					Type: QuotaTypeNetType,
// 					ScoreMap: map[string]int{
// 						"wifi": 10, // WiFi: 10分
// 						"5g":   8,  // 5G: 8分
// 						"4g":   6,  // 4G: 6分
// 						"3g":   3,  // 3G: 3分
// 						"2g":   1,  // 2G: 1分
// 					},
// 				},
// 				// 网络类型指标 - 图片场景 (50分)
// 				"net_type_pic": {
// 					Name: "图片网络类型",
// 					Desc: "图片场景网络类型评分",
// 					Type: QuotaTypeNetType,
// 					ScoreMap: map[string]int{
// 						"wifi": 50, // WiFi: 50分
// 						"5g":   40, // 5G: 40分
// 						"4g":   30, // 4G: 30分
// 						"3g":   15, // 3G: 15分
// 						"2g":   5,  // 2G: 5分
// 					},
// 				},
// 			},

// 			// 大区配置：map[大区ID]bool，命中配置则走评分逻辑，否则直接返回不降级
// 			AreaConfig: map[string]bool{
// 				"CN": true, // 中国大陆
// 				"HK": true, // 香港
// 				"TW": true, // 台湾
// 				"SG": true, // 新加坡
// 				"MY": true, // 马来西亚
// 				"TH": true, // 泰国
// 				"VN": true, // 越南
// 				"ID": true, // 印度尼西亚
// 				"PH": true, // 菲律宾
// 			},

// 			// 用户白名单：map[用户MID]bool，命中配置则返回不降级
// 			UserWhiteMap: map[string]bool{
// 				"123456789": true, // VIP用户1
// 				"987654321": true, // VIP用户2
// 			},

// 			// 用户黑名单：map[用户MID]bool，命中配置则返回降级
// 			UserBlackMap: map[string]bool{
// 				"111111111": true, // 黑名单用户1
// 				"222222222": true, // 黑名单用户2
// 			},

// 			// 房间白名单：map[房间ID]bool，命中配置则返回不降级
// 			RoomWhiteMap: map[string]bool{
// 				"1001": true, // VIP房间1
// 				"1002": true, // VIP房间2
// 			},

// 			// 房间黑名单：map[房间ID]bool，命中配置则返回降级
// 			RoomBlackMap: map[string]bool{
// 				"2001": true, // 黑名单房间1
// 				"2002": true, // 黑名单房间2
// 			},
// 		}
// 	})
// 	return dynamicLevelConfig
// }

// SetDynamicLevelConfig 设置动态分级配置（用于测试）
func SetDynamicLevelConfig(config *DynamicLevelConfig) {
	dynamicLevelConfig = config
}

package config

//审核开关
import (
	"fmt"
	"github.com/sirupsen/logrus"
	"zlutils/consul"
)

const ReviewConfConsulKey = "appstore_review_conf"
const ReviewConfConsulPrefix = "hiya/prod/service/global_conf/"
const ReviewConfConsulPrefixTest = "hiya/test/service/global_conf/"
const ReviewConfConsulPrefixDev = "hiya/dev/service/global_conf/"

type ReviewConf struct {
	Versions             []string `json:"versions"`               // 需要限制的版本（提审版本）
	Channels             []string `json:"channels"`               // 渠道
	LiveRooms            []int64  `json:"live_rooms"`             // 提审时显示的房间列表，最高优先级，覆盖其他配置
	LiveKinds            []int64  `json:"live_kinds"`             // 提审时显示Kind列表
	ShowIndex            bool     `json:"show_index"`             // 是否展示首页房间列表，为false时以上live_rooms才生效，否则也展示推荐房间
	ActivityEntry        bool     `json:"activity_entry"`         // 是否展示房间内活动入口（如扭蛋机，猜一猜）
	BannerEntry          bool     `json:"banner_entry"`           // 是否展示banner（主页轮播banner）
	ShowWithdraw         bool     `json:"show_withdraw"`          // 是否展示提现入口
	ShowMyIncome         bool     `json:"show_my_income"`         // 是否展示我的收益入口
	ScreenCaptureDisable bool     `json:"screen_capture_disable"` // 是否禁止截屏, 当版本为提审版本时(前面的version字段)，此字段不生效
	ShowFeedSquare       bool     `json:"show_feed_square"`       //是否展示动态帖子广场
	//FeedTagQuota         []FeedTagQuotaConf `json:"feed_tag_quota"`         // 帖子动态配比配置
	ShowRoomAlbum    bool    `json:"show_room_album"`
	ShowAccountAlbum bool    `json:"show_account_album"`
	WhiteMids        []int64 `json:"white_mids"` // 白名单mid
}

var rConf map[int32]*ReviewConf

func InitAppstoreReviewConf() {
	//注意：
	//依赖于调用者的consul.Init

	prefix := ReviewConfConsulPrefix
	if IsTest() {
		prefix = ReviewConfConsulPrefixTest
	} else if IsDev() {
		prefix = ReviewConfConsulPrefixDev
	}

	//监听列表变更
	consul.WithPrefix(prefix).WatchJson(ReviewConfConsulKey, &rConf, func() {
		logrus.Infof("appstore review configuration changed:%s", stringReviewConf())
	})
}

func stringReviewConf() string {
	s := ""
	for k, v := range rConf {
		s += fmt.Sprintf(" devType:%d conf:%+v", k, *v)
	}

	return s
}

func GetAllReviewConf() map[int32]*ReviewConf {
	return rConf
}

func GetReviewConf(devType int32) *ReviewConf {
	return rConf[devType]
}

// IsHitVersionByDevType 是否命中审核版本
func IsHitVersionByDevType(devType int32, version string) bool {
	c, ok := rConf[devType]
	if !ok {
		return false
	}
	for _, ver := range c.Versions {
		if ver == version {
			return true
		}
	}
	return false
}

type GetIsInReviewData struct {
	IsInReview bool `json:"is_in_review"`
}
type GetIsInReviewResp struct {
	Ret  int64              `json:"ret" msgpack:"ret"`
	Data *GetIsInReviewData `json:"data,omitempty"`
}

type BaseParam struct {
	App       string `json:"h_app,omitempty"`
	AppVer    string `json:"h_av"`
	HostVer   string `json:"h_av_host"` //宿主app版本号，仅当在sdk时有值
	DevType   int32  `json:"h_dt"`
	DevId     string `json:"h_did"`
	NetType   int32  `json:"h_nt"`
	MemId     int64  `json:"h_m,omitempty"`
	Chan      string `json:"h_ch,omitempty"`
	Timestamp int64  `json:"h_ts"`
	Model     string `json:"h_model"`
	Token     string `json:"token"`
}

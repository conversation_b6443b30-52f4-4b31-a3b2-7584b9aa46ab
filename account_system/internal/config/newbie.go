package config

import (
	logger "github.com/sirupsen/logrus"
	"zlutils/consul"
)

type NewbieConfig struct {
	WhiteMids    []int64  `json:"white_mids"`    // 新人白名单mid
	WhiteDids    []string `json:"white_dids"`    // 新人白名单did
	HavenCountry []string `json:"haven_country"` // 新人海湾地区双字母代码
	MinRt        int64    `json:"min_rt"`        // 最小注册时间，早于这个时间不算新用户
}

var DefaultNewbieConfig NewbieConfig
var NewbieWhiteMidMap = make(map[int64]interface{})
var NewbieWhiteDidMap = make(map[string]interface{})
var HavenCountryMap = make(map[string]interface{})

func initNewbieConfig() {
	consul.WatchJson("newbie.json", &DefaultNewbieConfig, func() {
		logger.WithField("new_value", DefaultNewbieConfig).Infof("newbie.json change")
		HavenCountryMap = make(map[string]interface{})
		NewbieWhiteMidMap = make(map[int64]interface{})
		NewbieWhiteDidMap = make(map[string]interface{})
		for _, country := range DefaultNewbieConfig.HavenCountry {
			HavenCountryMap[country] = nil
		}
		for _, mid := range DefaultNewbieConfig.WhiteMids {
			NewbieWhiteMidMap[mid] = nil
		}
		for _, did := range DefaultNewbieConfig.WhiteDids {
			NewbieWhiteDidMap[did] = nil
		}
	})
}

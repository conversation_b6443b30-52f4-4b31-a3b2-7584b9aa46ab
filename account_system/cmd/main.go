package main

import (
	"flag"
	"fmt"
	"github.com/sirupsen/logrus"
	"gitlab.xunlei.cn/hiya/common/xlrpc"
	"google.golang.org/grpc"
	"new-gitlab.xunlei.cn/funup/account_system/internal/config"
	"new-gitlab.xunlei.cn/funup/account_system/internal/proxy/recommend_tagging_grpc"
	"new-gitlab.xunlei.cn/funup/account_system/internal/service/account"
	"new-gitlab.xunlei.cn/funup/account_system/internal/service/app"
	"new-gitlab.xunlei.cn/funup/base_sdk"
	accountSdk "new-gitlab.xunlei.cn/funup/base_sdk/account"
	"new-gitlab.xunlei.cn/funup/i18n_sdk"
	"new-gitlab.xunlei.cn/funup/yaplay_proto/account_system"
	"zlutils/consul"
)

func getGrpcPort() int64 {
	return config.GrpcPort
}

func init() {
	logrus.SetFormatter(&logrus.JSONFormatter{
		DisableHTMLEscape: true,
		TimestampFormat:   "2006-01-02 15:03:04",
		PrettyPrint:       false,
	})
}

func gracefulStop(server *grpc.Server) {
	return
}

func main() {
	// 获取启动参数
	var configFile string
	flag.StringVar(&configFile, "f", "", "Configuration file.")
	flag.StringVar(&configFile, "c", "", "Configuration file.")
	flag.Parse()

	traceServerName := fmt.Sprintf("%s-%s", config.ProjectName, config.Env)
	xlrpc.InitJaeger(traceServerName, "10.4.19.43:6831")

	imp := app.NewAppService()
	accountImp := account.NewAccountService()
	registerServer := func(server *grpc.Server) {
		account_system.RegisterAppServiceServer(server, imp)
		account_system.RegisterAccountServiceServer(server, accountImp)
	}

	recommend_tagging_grpc.Init()
	i18n_sdk.InitCopyWriting()
	base_sdk.Init()
	accountSdk.InitBelongAreaCache(0, 0, 0)
	accountSdk.InitBatchBelongAreaCache(0, 0, 0)
	grpcServer := xlrpc.NewGrpcServer(
		getGrpcPort(), config.ProjectName, config.Env,
		registerServer, consul.Client, xlrpc.WithGracefulStop(gracefulStop),
	)

	err := grpcServer.Server()
	if err != nil {
		logrus.Panicf("grpc server start failed, err: %+v", err)
		return
	}
}
